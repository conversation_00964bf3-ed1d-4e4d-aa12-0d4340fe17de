import type { Task, Goal } from '../types';

/**
 * Calculate banana reward based on task priority and complexity
 */
export function calculateTaskBananas(priority: Task['priority'], hasDescription: boolean = false): number {
  const baseBananas = {
    low: 5,
    medium: 10,
    high: 15
  };
  
  let reward = baseBananas[priority];
  
  // Bonus for having a description (shows more thought)
  if (hasDescription) {
    reward += 2;
  }
  
  return reward;
}

/**
 * Calculate banana reward for goal milestones
 */
export function calculateGoalBananas(milestonesCount: number): number {
  // Base reward per milestone
  const baseReward = 25;
  
  // Bonus for larger goals
  const complexityBonus = Math.floor(milestonesCount / 3) * 10;
  
  return (baseReward * milestonesCount) + complexityBonus;
}

/**
 * Format task due date for display
 */
export function formatDueDate(date: Date): string {
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return 'Overdue';
  } else if (diffDays === 0) {
    return 'Due today';
  } else if (diffDays === 1) {
    return 'Due tomorrow';
  } else if (diffDays <= 7) {
    return `Due in ${diffDays} days`;
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * Check if task is overdue
 */
export function isTaskOverdue(task: Task): boolean {
  if (!task.dueDate || task.completed) return false;
  return new Date() > task.dueDate;
}

/**
 * Get task priority color
 */
export function getTaskPriorityColor(priority: Task['priority']): string {
  const colors = {
    low: '#10B981',    // Green
    medium: '#F59E0B', // Yellow
    high: '#EF4444'    // Red
  };
  return colors[priority];
}

/**
 * Sort tasks by priority and due date
 */
export function sortTasks(tasks: Task[]): Task[] {
  const priorityOrder = { high: 3, medium: 2, low: 1 };
  
  return [...tasks].sort((a, b) => {
    // First sort by completion status (incomplete first)
    if (a.completed !== b.completed) {
      return a.completed ? 1 : -1;
    }
    
    // Then by priority
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) return priorityDiff;
    
    // Finally by due date (earliest first)
    if (a.dueDate && b.dueDate) {
      return a.dueDate.getTime() - b.dueDate.getTime();
    } else if (a.dueDate) {
      return -1;
    } else if (b.dueDate) {
      return 1;
    }
    
    return 0;
  });
}

/**
 * Filter tasks by category
 */
export function filterTasksByCategory(tasks: Task[], category: string): Task[] {
  if (category === 'all') return tasks;
  return tasks.filter(task => task.category === category);
}

/**
 * Get unique categories from tasks
 */
export function getTaskCategories(tasks: Task[]): string[] {
  const categories = new Set(tasks.map(task => task.category || 'Uncategorized'));
  return Array.from(categories).sort();
}

/**
 * Calculate completion percentage for goals
 */
export function calculateGoalProgress(goal: Goal): number {
  if (goal.milestones.length === 0) return 0;
  const completedMilestones = goal.milestones.filter(m => m.completed).length;
  return Math.round((completedMilestones / goal.milestones.length) * 100);
}
