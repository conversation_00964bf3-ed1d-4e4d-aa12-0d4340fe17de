import { test, expect } from '@playwright/test';

test.describe('Complete User Workflow E2E Tests', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
    });

    test.describe('New User Onboarding Flow', () => {
        test('should guide new user through complete app experience', async ({ page }) => {
            // 1. Initial app load - game overlay should be visible
            const gameOverlay = page.locator('.game-overlay');
            await expect(gameOverlay).toBeVisible();
            await expect(page.locator('.focus-hint')).toContainText('Click anywhere to start playing');
            
            // 2. Start the game
            await gameOverlay.click();
            await page.waitForTimeout(1000);
            await expect(gameOverlay).toBeHidden();
            
            // 3. Check initial banana count
            const bananaCount = page.locator('.banana-count');
            const initialBananas = parseInt(await bananaCount.textContent() || '0');
            
            // 4. Create first task
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            
            await page.fill('input[placeholder*="task title"]', 'My First Task');
            await page.fill('textarea[placeholder*="description"]', 'Learning to use Banana Checklist');
            await page.selectOption('select[name="category"]', 'personal');
            await page.selectOption('select[name="priority"]', 'medium');
            await page.click('button:has-text("Add Task")');
            
            // 5. Verify task was created
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('text=My First Task')).toBeVisible();
            await expect(page.locator('text=1 task')).toBeVisible();
            
            // 6. Complete the task and earn bananas
            const checkbox = page.locator('input[type="checkbox"]').first();
            await checkbox.check();
            await page.waitForTimeout(1000);
            
            // 7. Verify banana reward
            const finalBananas = parseInt(await bananaCount.textContent() || '0');
            expect(finalBananas).toBeGreaterThan(initialBananas);
            
            // 8. Close task list
            await page.click('.close-btn');
            
            // 9. Test debug mode
            const debugButton = page.locator('button:has-text("Debug")');
            await debugButton.click();
            await expect(debugButton).toHaveClass(/active/);
        });
    });

    test.describe('Productivity Workflow', () => {
        test('should handle a typical productivity session', async ({ page }) => {
            // Dismiss game overlay
            const gameOverlay = page.locator('.game-overlay');
            if (await gameOverlay.isVisible()) {
                await gameOverlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Track initial banana count
            const bananaCount = page.locator('.banana-count');
            const initialBananas = parseInt(await bananaCount.textContent() || '0');
            
            // Create multiple tasks for different categories
            const tasks = [
                { title: 'Review project proposal', category: 'work', priority: 'high' },
                { title: 'Go for a run', category: 'health', priority: 'medium' },
                { title: 'Call mom', category: 'personal', priority: 'medium' },
                { title: 'Learn TypeScript', category: 'learning', priority: 'low' }
            ];
            
            // Create all tasks
            for (const task of tasks) {
                await page.click('button:has-text("Add Task")');
                await page.fill('input[placeholder*="task title"]', task.title);
                await page.selectOption('select[name="category"]', task.category);
                await page.selectOption('select[name="priority"]', task.priority);
                await page.click('button:has-text("Add Task")');
            }
            
            // Verify all tasks were created
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('text=4 tasks')).toBeVisible();
            
            for (const task of tasks) {
                await expect(page.locator(`text=${task.title}`)).toBeVisible();
            }
            
            // Complete high priority task first
            const highPriorityTask = page.locator('.task-item').filter({ hasText: 'Review project proposal' });
            const highPriorityCheckbox = highPriorityTask.locator('input[type="checkbox"]');
            await highPriorityCheckbox.check();
            
            // Complete medium priority task
            const mediumPriorityTask = page.locator('.task-item').filter({ hasText: 'Go for a run' });
            const mediumPriorityCheckbox = mediumPriorityTask.locator('input[type="checkbox"]');
            await mediumPriorityCheckbox.check();
            
            await page.waitForTimeout(1000);
            
            // Verify banana rewards
            const finalBananas = parseInt(await bananaCount.textContent() || '0');
            expect(finalBananas).toBeGreaterThan(initialBananas);
            
            // Filter to show only completed tasks
            await page.click('button:has-text("Completed")');
            await expect(page.locator('text=Review project proposal')).toBeVisible();
            await expect(page.locator('text=Go for a run')).toBeVisible();
            await expect(page.locator('text=Call mom')).toBeHidden();
            
            // Filter to show pending tasks
            await page.click('button:has-text("Pending")');
            await expect(page.locator('text=Call mom')).toBeVisible();
            await expect(page.locator('text=Learn TypeScript')).toBeVisible();
            await expect(page.locator('text=Review project proposal')).toBeHidden();
        });
    });

    test.describe('Task Management Workflow', () => {
        test('should handle complex task operations', async ({ page }) => {
            // Dismiss game overlay
            const gameOverlay = page.locator('.game-overlay');
            if (await gameOverlay.isVisible()) {
                await gameOverlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Create a task with detailed information
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Complex Project Task');
            await page.fill('textarea[placeholder*="description"]', 'This is a complex task with detailed requirements and multiple steps involved.');
            await page.selectOption('select[name="category"]', 'work');
            await page.selectOption('select[name="priority"]', 'urgent');
            await page.click('button:has-text("Add Task")');
            
            // Verify task details
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('text=Complex Project Task')).toBeVisible();
            await expect(page.locator('text=This is a complex task with detailed requirements')).toBeVisible();
            await expect(page.locator('text=work')).toBeVisible();
            await expect(page.locator('text=urgent')).toBeVisible();
            
            // Test task completion and uncommpletion
            const checkbox = page.locator('input[type="checkbox"]').first();
            await checkbox.check();
            await expect(checkbox).toBeChecked();
            
            // Uncomplete the task
            await checkbox.uncheck();
            await expect(checkbox).not.toBeChecked();
            
            // Complete it again
            await checkbox.check();
            await expect(checkbox).toBeChecked();
            
            // Test task deletion with confirmation
            page.on('dialog', dialog => dialog.accept());
            const deleteButton = page.locator('button:has-text("Delete")').first();
            await deleteButton.click();
            
            // Verify task was deleted
            await expect(page.locator('text=Complex Project Task')).toBeHidden();
            await expect(page.locator('text=0 tasks')).toBeVisible();
        });
    });

    test.describe('Game Integration Workflow', () => {
        test('should integrate game features with task management', async ({ page }) => {
            // Start the game
            const gameOverlay = page.locator('.game-overlay');
            if (await gameOverlay.isVisible()) {
                await gameOverlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Enable debug mode to see game elements
            const debugButton = page.locator('button:has-text("Debug")');
            await debugButton.click();
            await expect(debugButton).toHaveClass(/active/);
            
            // Create tasks while game is running
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Game Integration Task');
            await page.click('button:has-text("Add Task")');
            
            // Complete task to earn bananas
            await page.click('button:has-text("Tasks")');
            const checkbox = page.locator('input[type="checkbox"]').first();
            await checkbox.check();
            
            // Verify banana count updated
            const bananaCount = page.locator('.banana-count');
            const bananas = parseInt(await bananaCount.textContent() || '0');
            expect(bananas).toBeGreaterThan(0);
            
            // Close task list and interact with game
            await page.click('.close-btn');
            
            // Test keyboard controls while game is active
            const canvas = page.locator('canvas');
            await canvas.click();
            await page.keyboard.press('ArrowLeft');
            await page.keyboard.press('ArrowRight');
            await page.keyboard.press('Space');
            
            // Disable debug mode
            await debugButton.click();
            await expect(debugButton).not.toHaveClass(/active/);
            
            // Verify game state is maintained
            await expect(canvas).toBeVisible();
            await expect(bananaCount).toBeVisible();
        });
    });

    test.describe('Error Handling and Edge Cases', () => {
        test('should handle rapid user interactions gracefully', async ({ page }) => {
            // Dismiss game overlay
            const gameOverlay = page.locator('.game-overlay');
            if (await gameOverlay.isVisible()) {
                await gameOverlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Rapid overlay switching
            for (let i = 0; i < 5; i++) {
                await page.click('button:has-text("Add Task")');
                await page.waitForTimeout(100);
                await page.click('button:has-text("Tasks")');
                await page.waitForTimeout(100);
                await page.click('.close-btn');
                await page.waitForTimeout(100);
            }
            
            // App should still be functional
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            await page.fill('input[placeholder*="task title"]', 'Stress Test Task');
            await page.click('button:has-text("Add Task")');
            
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('text=Stress Test Task')).toBeVisible();
        });

        test('should maintain state consistency across interactions', async ({ page }) => {
            // Dismiss game overlay
            const gameOverlay = page.locator('.game-overlay');
            if (await gameOverlay.isVisible()) {
                await gameOverlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Create multiple tasks
            for (let i = 1; i <= 3; i++) {
                await page.click('button:has-text("Add Task")');
                await page.fill('input[placeholder*="task title"]', `Consistency Task ${i}`);
                await page.click('button:has-text("Add Task")');
            }
            
            // Complete middle task
            await page.click('button:has-text("Tasks")');
            const checkboxes = page.locator('input[type="checkbox"]');
            await checkboxes.nth(1).check(); // Complete second task
            
            // Verify state
            await expect(checkboxes.nth(0)).not.toBeChecked();
            await expect(checkboxes.nth(1)).toBeChecked();
            await expect(checkboxes.nth(2)).not.toBeChecked();
            
            // Close and reopen task list
            await page.click('.close-btn');
            await page.click('button:has-text("Tasks")');
            
            // State should be preserved
            const newCheckboxes = page.locator('input[type="checkbox"]');
            await expect(newCheckboxes.nth(0)).not.toBeChecked();
            await expect(newCheckboxes.nth(1)).toBeChecked();
            await expect(newCheckboxes.nth(2)).not.toBeChecked();
        });
    });
});
