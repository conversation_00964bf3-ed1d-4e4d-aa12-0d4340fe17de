

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.C0PkSLhF.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/Eaidhwvc.js","_app/immutable/chunks/QsgQHiSo.js","_app/immutable/chunks/C_1qu2iM.js","_app/immutable/chunks/C2NQNm1d.js"];
export const stylesheets = [];
export const fonts = [];
