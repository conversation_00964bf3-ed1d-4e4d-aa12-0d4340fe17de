import { writable, derived } from 'svelte/store';
import type { GameState, GameUpgrade, GameInput, FarmState } from '../types';

// Default farm state
const defaultFarmState: FarmState = {
    botLevel: 0,
    plantationLevel: 0,
    generationRate: 0,
    lastUpdateTime: Date.now(),
    totalGenerated: 0,
    isUnlocked: false
};

// Default game state
const defaultGameState: GameState = {
    monkeyPosition: { x: 100, y: 500 },
    currentScene: 'jungle',
    unlockedAreas: ['starting-grove'],
    farmData: defaultFarmState,
    cosmetics: {
        monkeySkin: 'default',
        theme: 'jungle'
    },
    upgrades: []
};

// Default input state
const defaultInput: GameInput = {
    left: false,
    right: false,
    up: false,
    down: false,
    jump: false,
    interact: false
};

function createGameStore() {
    const { subscribe, set, update } = writable<GameState>(defaultGameState);

    return {
        subscribe,
        set,
        updateMonkeyPosition: (x: number, y: number) => {
            update(state => ({
                ...state,
                monkeyPosition: { x, y }
            }));
        },
        changeScene: (scene: GameState['currentScene']) => {
            update(state => ({
                ...state,
                currentScene: scene
            }));
        },
        unlockArea: (area: string) => {
            update(state => ({
                ...state,
                unlockedAreas: [...state.unlockedAreas, area]
            }));
        },
        updateCosmetics: (cosmetics: Partial<GameState['cosmetics']>) => {
            update(state => ({
                ...state,
                cosmetics: { ...state.cosmetics, ...cosmetics }
            }));
        },
        purchaseUpgrade: (upgrade: GameUpgrade) => {
            update(state => ({
                ...state,
                upgrades: [...state.upgrades, { ...upgrade, purchased: true }]
            }));
        },
        unlockFarm: () => {
            update(state => ({
                ...state,
                farmData: state.farmData ? { ...state.farmData, isUnlocked: true } : { ...defaultFarmState, isUnlocked: true },
                unlockedAreas: [...state.unlockedAreas, 'banana-farm']
            }));
        },
        updateFarmData: (farmData: Partial<FarmState>) => {
            update(state => ({
                ...state,
                farmData: state.farmData ? { ...state.farmData, ...farmData } : { ...defaultFarmState, ...farmData }
            }));
        },
        calculateOfflineEarnings: () => {
            update(state => {
                if (!state.farmData || state.farmData.generationRate === 0) return state;

                const now = Date.now();
                const timeDiff = now - state.farmData.lastUpdateTime;
                const minutesOffline = timeDiff / (1000 * 60);
                const offlineEarnings = Math.floor(minutesOffline * state.farmData.generationRate);

                return {
                    ...state,
                    farmData: {
                        ...state.farmData,
                        lastUpdateTime: now,
                        totalGenerated: state.farmData.totalGenerated + offlineEarnings
                    }
                };
            });
        }
    };
}

function createInputStore() {
    const { subscribe, set, update } = writable<GameInput>(defaultInput);

    return {
        subscribe,
        set,
        setKey: (key: keyof GameInput, pressed: boolean) => {
            update(input => ({
                ...input,
                [key]: pressed
            }));
        },
        reset: () => set(defaultInput)
    };
}

export const gameStore = createGameStore();
export const inputStore = createInputStore();

// Available upgrades
export const GAME_UPGRADES: GameUpgrade[] = [
    {
        id: 'faster-monkey',
        name: 'Faster Monkey',
        description: '+10% movement speed',
        cost: 250,
        purchased: false,
        effect: 'speed_boost_10'
    },
    {
        id: 'banana-bots',
        name: 'Banana Bots',
        description: 'Auto-harvest 1 banana per minute',
        cost: 500,
        purchased: false,
        effect: 'auto_harvest_1',
        level: 1,
        maxLevel: 4
    },
    {
        id: 'double-jump',
        name: 'Double Jump',
        description: 'Jump twice in mid-air',
        cost: 750,
        purchased: false,
        effect: 'double_jump'
    },
    {
        id: 'banana-magnet',
        name: 'Banana Magnet',
        description: 'Automatically collect nearby bananas',
        cost: 1000,
        purchased: false,
        effect: 'auto_collect'
    }
];

// Farm-specific upgrades (managed separately for the NPC shop)
export const FARM_UPGRADES = {
    bots: [
        { level: 1, cost: 500, rate: 1, name: 'Basic Bot', description: '1 banana/minute' },
        { level: 2, cost: 1500, rate: 3, name: 'Advanced Bot', description: '3 bananas/minute' },
        { level: 3, cost: 3000, rate: 6, name: 'Super Bot', description: '6 bananas/minute' },
        { level: 4, cost: 6000, rate: 12, name: 'Mega Bot', description: '12 bananas/minute' }
    ],
    plantation: [
        { level: 1, cost: 2000, multiplier: 1.5, name: 'Small Plot', description: '+50% generation' },
        { level: 2, cost: 5000, multiplier: 2.0, name: 'Medium Farm', description: '+100% generation' },
        { level: 3, cost: 12000, multiplier: 3.0, name: 'Mega Plantation', description: '+200% generation' }
    ]
};

// Derived store for purchased upgrades
export const purchasedUpgrades = derived(
    gameStore,
    $gameState => $gameState.upgrades.filter(upgrade => upgrade.purchased)
);

// Derived store for available upgrades
export const availableUpgrades = derived(
    gameStore,
    $gameState => {
        const purchasedIds = $gameState.upgrades.map(u => u.id);
        return GAME_UPGRADES.filter(upgrade => !purchasedIds.includes(upgrade.id));
    }
);
