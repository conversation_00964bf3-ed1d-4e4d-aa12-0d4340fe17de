import { writable, derived } from 'svelte/store';
import type { GameState, GameUpgrade, GameInput } from '../types';

// Default game state
const defaultGameState: GameState = {
  monkeyPosition: { x: 100, y: 500 },
  currentScene: 'jungle',
  unlockedAreas: ['starting-grove'],
  cosmetics: {
    monkeySkin: 'default',
    theme: 'jungle'
  },
  upgrades: []
};

// Default input state
const defaultInput: GameInput = {
  left: false,
  right: false,
  up: false,
  down: false,
  jump: false,
  interact: false
};

function createGameStore() {
  const { subscribe, set, update } = writable<GameState>(defaultGameState);

  return {
    subscribe,
    set,
    updateMonkeyPosition: (x: number, y: number) => {
      update(state => ({
        ...state,
        monkeyPosition: { x, y }
      }));
    },
    changeScene: (scene: GameState['currentScene']) => {
      update(state => ({
        ...state,
        currentScene: scene
      }));
    },
    unlockArea: (area: string) => {
      update(state => ({
        ...state,
        unlockedAreas: [...state.unlockedAreas, area]
      }));
    },
    updateCosmetics: (cosmetics: Partial<GameState['cosmetics']>) => {
      update(state => ({
        ...state,
        cosmetics: { ...state.cosmetics, ...cosmetics }
      }));
    },
    purchaseUpgrade: (upgrade: GameUpgrade) => {
      update(state => ({
        ...state,
        upgrades: [...state.upgrades, { ...upgrade, purchased: true }]
      }));
    }
  };
}

function createInputStore() {
  const { subscribe, set, update } = writable<GameInput>(defaultInput);

  return {
    subscribe,
    set,
    setKey: (key: keyof GameInput, pressed: boolean) => {
      update(input => ({
        ...input,
        [key]: pressed
      }));
    },
    reset: () => set(defaultInput)
  };
}

export const gameStore = createGameStore();
export const inputStore = createInputStore();

// Available upgrades
export const GAME_UPGRADES: GameUpgrade[] = [
  {
    id: 'faster-monkey',
    name: 'Faster Monkey',
    description: '+10% movement speed',
    cost: 250,
    purchased: false,
    effect: 'speed_boost_10'
  },
  {
    id: 'banana-bots',
    name: 'Banana Bots',
    description: 'Auto-harvest 1 banana per minute',
    cost: 500,
    purchased: false,
    effect: 'auto_harvest_1'
  },
  {
    id: 'double-jump',
    name: 'Double Jump',
    description: 'Jump twice in mid-air',
    cost: 750,
    purchased: false,
    effect: 'double_jump'
  },
  {
    id: 'banana-magnet',
    name: 'Banana Magnet',
    description: 'Automatically collect nearby bananas',
    cost: 1000,
    purchased: false,
    effect: 'auto_collect'
  }
];

// Derived store for purchased upgrades
export const purchasedUpgrades = derived(
  gameStore,
  $gameState => $gameState.upgrades.filter(upgrade => upgrade.purchased)
);

// Derived store for available upgrades
export const availableUpgrades = derived(
  gameStore,
  $gameState => {
    const purchasedIds = $gameState.upgrades.map(u => u.id);
    return GAME_UPGRADES.filter(upgrade => !purchasedIds.includes(upgrade.id));
  }
);
