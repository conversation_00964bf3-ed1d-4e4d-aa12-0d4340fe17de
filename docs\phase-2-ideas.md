
# Expanded World & Scenes

* **Multiple Areas:** Introduce new game “levels” or environments. For example, beyond the initial jungle grove, add a village marketplace, a dark cave, a treetop canopy or even an underground banana laboratory. Users could *scroll offscreen* left/right (side-scrolling or multi-screen maps) to discover these areas. The code already hints at multiple layouts (e.g. `createJungleLevel()`, `createCaveLevel()`), so Phase 2 could flesh these out. Each area might correspond to task themes (e.g. a “Work Office” scene, a “Home Garden”) or game difficulty. New tilesets and backgrounds (forest, cave, mountain) would keep exploration fresh.

* **Seamless Map:** Instead of a single screen, allow the monkey to walk between connected zones. For example, reaching the right edge of one scene could fade into the next (like classic platformers). Hidden groves or secret paths could appear as easter eggs. This encourages users to explore for tasks: wandering off-path might reveal a side-quest or extra banana cache.

* **Dynamic Environment:** Add day/night cycles or weather effects (rain, storms) to make returning users feel progression over time. For instance, bananas could grow on trees over real hours, or certain tasks only appear at night. This ties into *incremental mechanics* (next section) by making the world feel alive.

## NPC Characters & Quest Givers

* **Monkey Villagers and Friends:** Populate the world with friendly NPCs (other monkeys, parrots, jungle animals) who serve as interfaces to game features. For example, a *Monkey Elder* could offer daily “quest” tasks (e.g. “Complete 5 tasks today” or “Finish a Habit streak”) in exchange for bonus bananas. A *Vendor Monkey* might sell upgrades or power-ups (like “Banana Bots” that auto-harvest bananas) when you bring him bananas. These NPCs make the game feel social and guide users, rather than relying on static menus.

* **Quest Boards/Maps:** Use NPCs or in-game objects as task-givers. For instance, a “jungle map board” in a village could list active quests (daily goals, mini-challenges), and talking to it (via a click or tap) opens the task list. This embeds tasks into the game world: picking up a quest scroll from a tree or shop ties the checklist to exploration. Rewards from NPCs reinforce the task loop: completing real to-dos could unlock new dialogue or animations from these characters.

* **Character Customization:** Let NPCs reflect progress. For casual gamers, collecting new outfits or hats (e.g. a golden banana crown or adventurer’s vest) is fun; for “productivity nerds,” earning these as badges validates effort. Already, *cosmetics* (skins, hats) are in the plan. Phase 2 could expand this: unlockable monkey pets or compadre sidekicks (mini-monkeys or parrots) that accompany the player when tasks are completed. Such companions might celebrate with extra sparkles or carry a secondary banana pouch.

## Gameplay & Incremental Mechanics

* **Idle/Clicker Features:** Introduce light idle-game elements. For instance, allow **passive banana generation** (an idle income) when the player is offline or idle. Upgrades like “Banana Bots: Auto-harvest 1 banana/hr” are already envisioned; we could extend this with buildings (e.g. plant a banana farm in the village that yields bananas over time). This gives “incremental” feel – even tap outside the game, bananas trickle in. Similarly, tapping or clicking bananas during play could yield combo bonuses (e.g. tapping 10 bananas in a row gives a spree bonus).

* **Progression & Prestige:** Add layers of progression common in clicker games. For example, allow a “prestige” reset after collecting a large number of bananas: reset the level/map but gain a multiplier or permanent bonus (e.g. each banana is worth 1.1×). Each prestige could unlock new cosmetic themes (neon jungle, space sandbox) or permanent upgrades. This echoes *endless growth* found in incremental games like **(the) Gnorp Apologue**, which engages players by showing thousands of little critters (gnorps) continuously mining a rock. Similarly, dozens of animated banana-harvesting monkeys could swarm the screen as you upgrade, making the numbers feel satisfying to watch.

* **New Mini-Games:** Beyond the ones already planned, Phase 2 could add variety. Ideas include a “Banana Blend” puzzle (match 3 fruits for points), a simple rhythm mini-game (tap in time to monkey drum beats to gain bonuses), or an “Archaeology Dig” (spend bananas to unearth rare artifacts). Each mini-game should tie back to tasks (correct moves auto-complete micro-tasks) or earn extra bananas. This keeps casual players engaged with quick, repeatable challenges, while also rewarding routine tasks.

* **Skill Tree & Upgrades:** Flesh out an upgrade system. In addition to obvious stats (speed, jump height), introduce meta-upgrades such as “Task Efficiency” (earning extra bananas for higher-priority tasks) or “Focus Mode” (temporarily double rewards). A skill tree or upgrade shop (marketplace NPC) adds long-term goals. The *Bounty Tasker* app showed how RPG elements can blend seamlessly: completing to-dos grants XP, which you spend on character loot or skills. We can do the same with bananas/coins earned by tasks used to “buy” perks.

## Task Integration & Gamification

* **Tasks as Quests:** Treat each to-do item or goal as an in-game *quest*. For example, adding a real-world task could generate a quest dialog (“Monkey Elder: Help me collect 5 lost bananas by tomorrow!”) and planting a task-tree in the world. Completing the to-do triggers the in-game quest reward. This leverages the principle from Habitica and other gamified planners: real actions deal damage to monsters or gather loot. In our case, the “monster” could be a pile of tasks that shrinks as you complete them.

* **Gamified UI:** Integrate the checklist UI into the game screen. Rather than a separate panel, imagine the task list drawn on a jungle parchment or signpost in the game that the player character can interact with. Upon interaction, a stylized menu appears (banana bunch icons for each task). This keeps the experience unified. Similar ideas exist in tools like the *Obsidian Gamified Tasks* plugin, which literally “merges task management with a rewards system”. We can adapt its mechanics: tasks have difficulty ratings (hard tasks yield more bananas) or counters for subtasks (break tasks into smaller “banana steps”).

* **Achievements & Badges:** Expand the achievement system. Beyond counting 100 tasks or 1000 bananas, add specific challenges: “Finish all Today’s Tasks before noon,” or “Complete a streak without using Act Busy.” Granting rewards (unique hats, pet monkeys, or temporary multipliers) for these builds motivation. Visual progress bars (for daily/weekly tasks or banana totals) already feature in design and could be made more prominent (e.g. a giant vine growing as you progress through tasks).

* **Feedback & Rewards:** Make task completion **viscerally satisfying**. Animations like a *golden banana burst* when a to-do is checked off are in the spec; ensure they’re prominent. Consider also rewarding productive periods with combo effects (e.g. completing multiple tasks in a row triggers a “Banana Frenzy” bonus). Incorporate sound (chiptune jingles or cheerful monkey hoots) for big milestones. The continuous *“coins/bananas for tasks”* loop should feel as fun as slotting a piece in Tetris or defeating an enemy, but in a way that validates real work.

## Visual Style, UI & Input

&#x20;*Figure: Example pixel-art monkey sprite (from a free jungle platformer asset pack).*
We’ll continue the 16-bit pixel-art aesthetic described in Phase 1, but with richer animations and UI. For example, using **banana-themed buttons** (peel-shaped checkboxes, vine-wrapped panels) and screen-space **particle effects** (sparkles when bananas appear) can reinforce the theme. We could even toggle a retro CRT filter for fun, as planned.

* **Cross-Platform Input:** Ensure controls suit every device. On desktop/web, use keyboard and mouse/touchpad: arrow keys or WASD for movement and spacebar for jumping (as in Phase 1), plus clickable on-screen UI for tasks. On mobile/tablet, implement on-screen joystick/buttons or gesture controls: swiping or virtual buttons for movement and taps for interactions. PixiJS (used by our engine) easily supports touch, and the design’s 800×600 canvas can scale to different screens. Because deployment uses SvelteKit with Capacitor, it already targets iOS and Android alongside web.

* **Responsive Layout:** The task checklist UI should be legible on small screens. We might present tasks as a scrollable list on one side of the game canvas, or allow “pause” mode to review tasks. Likewise, visual elements (text, icons) should scale or simplify on mobile to avoid clutter. Offline support (caching tasks in local storage) ensures work continues if connectivity drops.

* **Obsidian-Style Gamification:** Borrow ideas from productivity tools. For instance, the *Obsidian Gamified Tasks* plugin awards coins for tasks; similarly, we can show a coin counter or level meter that fills as tasks are done. Difficulty settings on tasks (easy/medium/hard) could modify banana rewards. These features will particularly appeal to “productivity nerds” by giving clear feedback on effort, while casual gamers just enjoy leveling up their monkey avatar and seeing coins accumulate.

* **Accessibility:** Keep input simple and optional. Provide both tap/click and keyboard shortcuts, allow control sensitivity adjustments, and include optional auto-collect features (so tasks can be completed in the background). This helps ensure the game is fun on both desktop and touch devices.

## Social & Collaboration (Optional)

* **Shared Challenges:** To engage more users, consider social features: e.g. a shared **party quest**, where a group of friends each completes tasks to defeat a “boss banana bandit.” This mirrors Habitica’s party quests concept. It could be phase 2 stretch: earning double bananas for tasks when collaborating. Leaderboards or guilds could also attract competitive or cooperative players.

* **Reminders & Integrations:** For productivity nerds, integrate with calendars or notifications: reminders could take the form of in-game alerts (the Monkey Elder reminding the player of an overdue task). A Pomodoro focus mode might be gamified as a “meditation minigame” where you keep the monkey still while a timer counts down, earning bonus bananas.

Each of these ideas should be evaluated for fun and feasibility, but together they flesh out the world and make tasks feel woven into the game. For example, NPC quest givers and multi-scene exploration have precedent (“NPC monkeys offering quests”), and incremental upgrades (like banana bots) are already planned. By building on those, Phase 2 can truly blur the line between the checklist and the game, appealing to both casual gamers and productivity enthusiasts.

**Sources:** We drew inspiration from existing designs (e.g. Habitica, Bounty Tasker, Obsidian plugins), and from incremental games like *the Gnorp Apologue*. The current PRD also outlines core concepts (pixel-art style, quest trees, banana rewards); Phase 2 ideas extend those in creative directions. All quotes above come from the Banana Checklist project docs and related resources.
