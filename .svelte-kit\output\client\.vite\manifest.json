{".svelte-kit/generated/client-optimized/app.js": {"file": "_app/immutable/entry/app.HZc8k4-V.js", "name": "entry/app", "src": ".svelte-kit/generated/client-optimized/app.js", "isEntry": true, "imports": ["_yuiQqa5R.js", "_QsgQHiSo.js", "_C_1qu2iM.js", "_CWj6FrbW.js"], "dynamicImports": [".svelte-kit/generated/client-optimized/nodes/0.js", ".svelte-kit/generated/client-optimized/nodes/1.js", ".svelte-kit/generated/client-optimized/nodes/2.js"]}, ".svelte-kit/generated/client-optimized/nodes/0.js": {"file": "_app/immutable/nodes/0.s2bASigq.js", "name": "nodes/0", "src": ".svelte-kit/generated/client-optimized/nodes/0.js", "isEntry": true, "isDynamicEntry": true, "imports": ["_CWj6FrbW.js", "_QsgQHiSo.js"]}, ".svelte-kit/generated/client-optimized/nodes/1.js": {"file": "_app/immutable/nodes/1.C0PkSLhF.js", "name": "nodes/1", "src": ".svelte-kit/generated/client-optimized/nodes/1.js", "isEntry": true, "isDynamicEntry": true, "imports": ["_CWj6FrbW.js", "_Eaidhwvc.js", "_QsgQHiSo.js", "_C_1qu2iM.js", "_C2NQNm1d.js"]}, ".svelte-kit/generated/client-optimized/nodes/2.js": {"file": "_app/immutable/nodes/2.DTKhn8Zg.js", "name": "nodes/2", "src": ".svelte-kit/generated/client-optimized/nodes/2.js", "isEntry": true, "imports": ["_BgykbHj9.js"]}, "_2.q15tuB_a.css": {"file": "_app/immutable/assets/2.q15tuB_a.css", "src": "_2.q15tuB_a.css"}, "_BgykbHj9.js": {"file": "_app/immutable/chunks/BgykbHj9.js", "name": "2", "isDynamicEntry": true, "imports": ["_CWj6FrbW.js", "_Eaidhwvc.js", "_QsgQHiSo.js", "_C_1qu2iM.js", "_yuiQqa5R.js"], "dynamicImports": ["node_modules/pixi.js/lib/environment-browser/browserAll.mjs", "node_modules/pixi.js/lib/environment-webworker/webworkerAll.mjs", "node_modules/pixi.js/lib/rendering/renderers/gpu/WebGPURenderer.mjs", "node_modules/pixi.js/lib/rendering/renderers/gl/WebGLRenderer.mjs"], "css": ["_app/immutable/assets/2.q15tuB_a.css"]}, "_C2NQNm1d.js": {"file": "_app/immutable/chunks/C2NQNm1d.js", "name": "entry", "imports": ["_C_1qu2iM.js", "_QsgQHiSo.js"]}, "_CWj6FrbW.js": {"file": "_app/immutable/chunks/CWj6FrbW.js", "name": "disclose-version"}, "_C_1qu2iM.js": {"file": "_app/immutable/chunks/C_1qu2iM.js", "name": "index-client", "imports": ["_QsgQHiSo.js"]}, "_Cv0AAtAs.js": {"file": "_app/immutable/chunks/Cv0AAtAs.js", "name": "colorToUniform", "imports": ["_BgykbHj9.js"]}, "_DIetW8d-.js": {"file": "_app/immutable/chunks/DIetW8d-.js", "name": "SharedSystems", "imports": ["_BgykbHj9.js", "_Cv0AAtAs.js"]}, "_DuMrozzS.js": {"file": "_app/immutable/chunks/DuMrozzS.js", "name": "CanvasPool", "imports": ["_Cv0AAtAs.js", "_BgykbHj9.js"]}, "_Eaidhwvc.js": {"file": "_app/immutable/chunks/Eaidhwvc.js", "name": "legacy", "imports": ["_QsgQHiSo.js"]}, "_QsgQHiSo.js": {"file": "_app/immutable/chunks/QsgQHiSo.js", "name": "snippet"}, "_yuiQqa5R.js": {"file": "_app/immutable/chunks/yuiQqa5R.js", "name": "preload-helper", "imports": ["_QsgQHiSo.js", "_C_1qu2iM.js"]}, "node_modules/@sveltejs/kit/src/runtime/client/entry.js": {"file": "_app/immutable/entry/start.CM8QSaDT.js", "name": "entry/start", "src": "node_modules/@sveltejs/kit/src/runtime/client/entry.js", "isEntry": true, "imports": ["_C2NQNm1d.js"]}, "node_modules/pixi.js/lib/environment-browser/browserAll.mjs": {"file": "_app/immutable/chunks/DvBGuWSy.js", "name": "browserAll", "src": "node_modules/pixi.js/lib/environment-browser/browserAll.mjs", "isDynamicEntry": true, "imports": ["_BgykbHj9.js", "node_modules/pixi.js/lib/environment-webworker/webworkerAll.mjs"]}, "node_modules/pixi.js/lib/environment-webworker/webworkerAll.mjs": {"file": "_app/immutable/chunks/Cs1CBsQE.js", "name": "webworkerAll", "src": "node_modules/pixi.js/lib/environment-webworker/webworkerAll.mjs", "isDynamicEntry": true, "imports": ["_BgykbHj9.js", "_DuMrozzS.js", "_Cv0AAtAs.js"]}, "node_modules/pixi.js/lib/rendering/renderers/gl/WebGLRenderer.mjs": {"file": "_app/immutable/chunks/4ieXAFZF.js", "name": "WebGLRenderer", "src": "node_modules/pixi.js/lib/rendering/renderers/gl/WebGLRenderer.mjs", "isDynamicEntry": true, "imports": ["_BgykbHj9.js", "_Cv0AAtAs.js", "_DIetW8d-.js"]}, "node_modules/pixi.js/lib/rendering/renderers/gpu/WebGPURenderer.mjs": {"file": "_app/immutable/chunks/DcAf90n_.js", "name": "WebGPUR<PERSON>er", "src": "node_modules/pixi.js/lib/rendering/renderers/gpu/WebGPURenderer.mjs", "isDynamicEntry": true, "imports": ["_BgykbHj9.js", "_DuMrozzS.js", "_Cv0AAtAs.js", "_DIetW8d-.js"]}}