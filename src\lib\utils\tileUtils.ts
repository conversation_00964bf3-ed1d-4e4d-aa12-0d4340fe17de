import * as PIXI from 'pixi.js';

/**
 * Tile management utilities for creating game worlds with tilesets
 */

export interface TileInfo {
    id: number;
    x: number;
    y: number;
    width: number;
    height: number;
    texture: PIXI.Texture;
}

export interface TilesetConfig {
    tileWidth: number;
    tileHeight: number;
    tilesPerRow: number;
    totalTiles: number;
}

export interface WorldTile {
    tileId: number;
    worldX: number;
    worldY: number;
    sprite?: PIXI.Sprite;
}

/**
 * TilesetManager handles loading and managing tileset sprites
 */
export class TilesetManager {
    private tileset: PIXI.Texture | null = null;
    private tiles: Map<number, TileInfo> = new Map();
    private config: TilesetConfig;

    constructor(config: TilesetConfig) {
        this.config = config;
    }

    /**
     * Load the tileset texture and extract individual tile textures
     */
    async loadTileset(tilesetPath: string): Promise<void> {
        try {
            this.tileset = await PIXI.Assets.load(tilesetPath);
            // eslint-disable-next-line no-console
            console.log(`Tileset loaded: ${this.tileset.width}x${this.tileset.height}`);

            this.extractTiles();
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to load tileset:', error);
            throw error;
        }
    }

    /**
     * Extract individual tile textures from the tileset
     */
    private extractTiles(): void {
        if (!this.tileset) {
            throw new Error('Tileset not loaded');
        }

        const { tileWidth, tileHeight, tilesPerRow } = this.config;
        let tileId = 0;

        // Calculate how many rows we have
        const totalRows = Math.floor(this.tileset.height / tileHeight);

        for (let row = 0; row < totalRows; row++) {
            for (let col = 0; col < tilesPerRow; col++) {
                const x = col * tileWidth;
                const y = row * tileHeight;

                // Check if we're within the texture bounds
                if (x + tileWidth <= this.tileset.width && y + tileHeight <= this.tileset.height) {
                    const tileTexture = new PIXI.Texture({
                        source: this.tileset.source,
                        frame: new PIXI.Rectangle(x, y, tileWidth, tileHeight)
                    });

                    const tileInfo: TileInfo = {
                        id: tileId,
                        x,
                        y,
                        width: tileWidth,
                        height: tileHeight,
                        texture: tileTexture
                    };

                    this.tiles.set(tileId, tileInfo);
                    tileId++;
                }
            }
        }

        // eslint-disable-next-line no-console
        console.log(`Extracted ${this.tiles.size} tiles from tileset`);
    }

    /**
     * Get a tile texture by ID
     */
    getTile(tileId: number): PIXI.Texture | null {
        const tile = this.tiles.get(tileId);
        return tile ? tile.texture : null;
    }

    /**
     * Get tile info by ID
     */
    getTileInfo(tileId: number): TileInfo | null {
        return this.tiles.get(tileId) || null;
    }

    /**
     * Get all available tile IDs
     */
    getAvailableTileIds(): number[] {
        return Array.from(this.tiles.keys());
    }

    /**
     * Create a sprite from a tile ID
     */
    createTileSprite(tileId: number, scale: number = 1): PIXI.Sprite | null {
        const texture = this.getTile(tileId);
        if (!texture) {
            // eslint-disable-next-line no-console
            console.warn(`Tile ${tileId} not found`);
            return null;
        }

        const sprite = new PIXI.Sprite(texture);
        sprite.scale.set(scale, scale);
        return sprite;
    }

    /**
     * Check if tileset is loaded
     */
    isLoaded(): boolean {
        return this.tileset !== null && this.tiles.size > 0;
    }
}

/**
 * WorldBuilder helps create game worlds using tiles
 */
export class WorldBuilder {
    private tilesetManager: TilesetManager;
    private worldContainer: PIXI.Container;
    private worldTiles: WorldTile[] = [];

    constructor(tilesetManager: TilesetManager) {
        this.tilesetManager = tilesetManager;
        this.worldContainer = new PIXI.Container();
    }

    /**
     * Get the world container
     */
    getContainer(): PIXI.Container {
        return this.worldContainer;
    }

    /**
     * Add a tile to the world at specific coordinates
     */
    addTile(tileId: number, worldX: number, worldY: number, scale: number = 2): PIXI.Sprite | null {
        const sprite = this.tilesetManager.createTileSprite(tileId, scale);
        if (!sprite) {
            return null;
        }

        sprite.x = worldX;
        sprite.y = worldY;

        const worldTile: WorldTile = {
            tileId,
            worldX,
            worldY,
            sprite
        };

        this.worldTiles.push(worldTile);
        this.worldContainer.addChild(sprite);

        return sprite;
    }

    /**
     * Create a platform using tiles
     */
    createPlatform(tileId: number, startX: number, startY: number, length: number, scale: number = 2): PIXI.Sprite[] {
        const sprites: PIXI.Sprite[] = [];
        const tileInfo = this.tilesetManager.getTileInfo(tileId);

        if (!tileInfo) {
            // eslint-disable-next-line no-console
            console.warn(`Cannot create platform: tile ${tileId} not found`);
            return sprites;
        }

        const tileWidth = tileInfo.width * scale;

        for (let i = 0; i < length; i++) {
            const sprite = this.addTile(tileId, startX + (i * tileWidth), startY, scale);
            if (sprite) {
                sprites.push(sprite);
            }
        }

        return sprites;
    }

    /**
     * Clear all tiles from the world
     */
    clearWorld(): void {
        this.worldContainer.removeChildren();
        this.worldTiles = [];
    }

    /**
     * Get all world tiles
     */
    getWorldTiles(): WorldTile[] {
        return [...this.worldTiles];
    }

    /**
     * Get tiles in a specific area (for collision detection)
     */
    getTilesInArea(x: number, y: number, width: number, height: number): WorldTile[] {
        return this.worldTiles.filter(tile => {
            if (!tile.sprite) return false;

            return (
                tile.sprite.x < x + width &&
                tile.sprite.x + tile.sprite.width > x &&
                tile.sprite.y < y + height &&
                tile.sprite.y + tile.sprite.height > y
            );
        });
    }
}

/**
 * Common tile configurations for the Pixel Jungle tileset
 */
export const JUNGLE_TILESET_CONFIG: TilesetConfig = {
    tileWidth: 16,
    tileHeight: 16,
    tilesPerRow: 16, // This may need adjustment based on actual tileset
    totalTiles: 256 // This may need adjustment based on actual tileset
};

/**
 * Common tile IDs (these will need to be determined by examining the actual tileset)
 */
export const COMMON_TILES = {
    GRASS_TOP: 0,
    GRASS_MIDDLE: 1,
    DIRT: 2,
    STONE: 3,
    PLATFORM_LEFT: 4,
    PLATFORM_MIDDLE: 5,
    PLATFORM_RIGHT: 6,
    TREE_TRUNK: 7,
    LEAVES: 8,
    VINE: 9
} as const;
