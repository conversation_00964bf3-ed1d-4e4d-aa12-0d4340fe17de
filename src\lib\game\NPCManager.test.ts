import { describe, it, expect, beforeEach, vi } from 'vitest';
import * as PIXI from 'pixi.js';
import { NPCManager, createBananaBotNPC } from './NPCManager';
import type { NPC } from '../types';

// Mock PIXI Application
const mockApp = {
    stage: {
        addChild: vi.fn(),
        removeChild: vi.fn(),
        sortableChildren: true
    }
} as unknown as PIXI.Application;

describe('NPCManager', () => {
    let npcManager: NPCManager;

    beforeEach(() => {
        vi.clearAllMocks();
        npcManager = new NPCManager(mockApp);
    });

    describe('addNPC', () => {
        it('should add an NPC to the scene', () => {
            const testNPC: NPC = {
                id: 'test-npc',
                name: 'Test NPC',
                type: 'vendor',
                position: { x: 100, y: 200 },
                spriteSheet: 'test.png',
                dialogues: []
            };

            npcManager.addNPC(testNPC);

            // Should add sprite and interaction zone to container
            expect(mockApp.stage.addChild).toHaveBeenCalledTimes(1);
        });

        it('should create banana bot sprite correctly', () => {
            const bananaBotNPC = createBananaBotNPC({ x: 300, y: 400 });
            
            npcManager.addNPC(bananaBotNPC);

            // Verify NPC was added
            expect(mockApp.stage.addChild).toHaveBeenCalledTimes(1);
        });
    });

    describe('update', () => {
        it('should update NPC proximity states', () => {
            const testNPC: NPC = {
                id: 'test-npc',
                name: 'Test NPC',
                type: 'vendor',
                position: { x: 100, y: 100 },
                spriteSheet: 'test.png',
                dialogues: []
            };

            npcManager.addNPC(testNPC);

            // Player far away
            npcManager.update({ x: 200, y: 200 });
            
            // Player nearby
            npcManager.update({ x: 110, y: 110 });

            // Should not throw errors
            expect(true).toBe(true);
        });
    });

    describe('removeNPC', () => {
        it('should remove an NPC from the scene', () => {
            const testNPC: NPC = {
                id: 'test-npc',
                name: 'Test NPC',
                type: 'vendor',
                position: { x: 100, y: 200 },
                spriteSheet: 'test.png',
                dialogues: []
            };

            npcManager.addNPC(testNPC);
            npcManager.removeNPC('test-npc');

            // Should not throw errors
            expect(true).toBe(true);
        });
    });

    describe('clearAllNPCs', () => {
        it('should clear all NPCs from the scene', () => {
            const testNPC1: NPC = {
                id: 'test-npc-1',
                name: 'Test NPC 1',
                type: 'vendor',
                position: { x: 100, y: 200 },
                spriteSheet: 'test.png',
                dialogues: []
            };

            const testNPC2: NPC = {
                id: 'test-npc-2',
                name: 'Test NPC 2',
                type: 'quest-giver',
                position: { x: 300, y: 400 },
                spriteSheet: 'test.png',
                dialogues: []
            };

            npcManager.addNPC(testNPC1);
            npcManager.addNPC(testNPC2);
            npcManager.clearAllNPCs();

            // Should not throw errors
            expect(true).toBe(true);
        });
    });

    describe('setInteractionCallback', () => {
        it('should set interaction callback', () => {
            const callback = vi.fn();
            npcManager.setInteractionCallback(callback);

            // Should not throw errors
            expect(true).toBe(true);
        });
    });
});

describe('createBananaBotNPC', () => {
    it('should create a banana bot NPC with correct properties', () => {
        const position = { x: 500, y: 600 };
        const bananaBotNPC = createBananaBotNPC(position);

        expect(bananaBotNPC.id).toBe('banana-bot');
        expect(bananaBotNPC.name).toBe('Banana Bot');
        expect(bananaBotNPC.type).toBe('vendor');
        expect(bananaBotNPC.position).toEqual(position);
        expect(bananaBotNPC.dialogues).toHaveLength(3);
        expect(bananaBotNPC.shop).toBeDefined();
        expect(bananaBotNPC.shop?.items).toHaveLength(2);
    });

    it('should have correct dialogue structure', () => {
        const bananaBotNPC = createBananaBotNPC({ x: 0, y: 0 });
        
        const greetingDialogue = bananaBotNPC.dialogues.find(d => d.id === 'greeting');
        expect(greetingDialogue).toBeDefined();
        expect(greetingDialogue?.text).toContain('Banana Bot');
        expect(greetingDialogue?.responses).toHaveLength(3);
    });

    it('should have correct shop items', () => {
        const bananaBotNPC = createBananaBotNPC({ x: 0, y: 0 });
        
        expect(bananaBotNPC.shop?.currency).toBe('bananas');
        
        const botUpgrade = bananaBotNPC.shop?.items.find(item => item.id === 'bot-upgrade-1');
        expect(botUpgrade).toBeDefined();
        expect(botUpgrade?.cost).toBe(500);
        expect(botUpgrade?.category).toBe('bot');

        const plantationUpgrade = bananaBotNPC.shop?.items.find(item => item.id === 'plantation-upgrade-1');
        expect(plantationUpgrade).toBeDefined();
        expect(plantationUpgrade?.cost).toBe(2000);
        expect(plantationUpgrade?.category).toBe('plantation');
    });
});
