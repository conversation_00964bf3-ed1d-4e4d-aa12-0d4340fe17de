import { writable, derived } from 'svelte/store';
import type { Task, Goal } from '../types';

// Task store
function createTaskStore() {
  const { subscribe, set, update } = writable<Task[]>([]);

  return {
    subscribe,
    set,
    add: (task: Omit<Task, 'id' | 'createdAt'>) => {
      const newTask: Task = {
        ...task,
        id: crypto.randomUUID(),
        createdAt: new Date(),
      };
      update(tasks => [...tasks, newTask]);
      return newTask;
    },
    update: (id: string, updates: Partial<Task>) => {
      update(tasks => 
        tasks.map(task => 
          task.id === id ? { ...task, ...updates } : task
        )
      );
    },
    complete: (id: string) => {
      update(tasks => 
        tasks.map(task => 
          task.id === id 
            ? { ...task, completed: true, completedAt: new Date() }
            : task
        )
      );
    },
    delete: (id: string) => {
      update(tasks => tasks.filter(task => task.id !== id));
    },
    clear: () => set([])
  };
}

// Goal store
function createGoalStore() {
  const { subscribe, set, update } = writable<Goal[]>([]);

  return {
    subscribe,
    set,
    add: (goal: Omit<Goal, 'id' | 'createdAt'>) => {
      const newGoal: Goal = {
        ...goal,
        id: crypto.randomUUID(),
        createdAt: new Date(),
      };
      update(goals => [...goals, newGoal]);
      return newGoal;
    },
    update: (id: string, updates: Partial<Goal>) => {
      update(goals => 
        goals.map(goal => 
          goal.id === id ? { ...goal, ...updates } : goal
        )
      );
    },
    delete: (id: string) => {
      update(goals => goals.filter(goal => goal.id !== id));
    },
    clear: () => set([])
  };
}

export const taskStore = createTaskStore();
export const goalStore = createGoalStore();

// Derived stores for computed values
export const completedTasks = derived(
  taskStore,
  $tasks => $tasks.filter(task => task.completed)
);

export const pendingTasks = derived(
  taskStore,
  $tasks => $tasks.filter(task => !task.completed)
);

export const tasksByCategory = derived(
  taskStore,
  $tasks => {
    const categories: Record<string, Task[]> = {};
    $tasks.forEach(task => {
      const category = task.category || 'Uncategorized';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(task);
    });
    return categories;
  }
);

export const totalBananasEarned = derived(
  completedTasks,
  $completedTasks => $completedTasks.reduce((total, task) => total + task.bananaReward, 0)
);
