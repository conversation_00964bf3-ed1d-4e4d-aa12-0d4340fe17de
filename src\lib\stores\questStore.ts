import { writable, derived } from 'svelte/store';
import type { Quest } from '../types';

// Sample quests
const defaultQuests: Quest[] = [
  {
    id: 'daily-tasks-3',
    title: 'Daily Productivity',
    description: 'Complete 3 tasks today',
    type: 'daily',
    requirements: [
      {
        type: 'complete_tasks',
        target: 3,
        current: 0
      }
    ],
    bananaReward: 25,
    completed: false,
    progress: 0,
    maxProgress: 3
  },
  {
    id: 'weekly-streak-7',
    title: 'Consistency Champion',
    description: 'Maintain a 7-day task completion streak',
    type: 'weekly',
    requirements: [
      {
        type: 'maintain_streak',
        target: 7,
        current: 0
      }
    ],
    bananaReward: 100,
    completed: false,
    progress: 0,
    maxProgress: 7
  },
  {
    id: 'achievement-100-tasks',
    title: 'Century Club',
    description: 'Complete 100 tasks total',
    type: 'achievement',
    requirements: [
      {
        type: 'complete_tasks',
        target: 100,
        current: 0
      }
    ],
    bananaReward: 500,
    completed: false,
    progress: 0,
    maxProgress: 100
  }
];

function createQuestStore() {
  const { subscribe, set, update } = writable<Quest[]>(defaultQuests);

  return {
    subscribe,
    set,
    updateProgress: (questId: string, progress: number) => {
      update(quests => 
        quests.map(quest => {
          if (quest.id === questId) {
            const newProgress = Math.min(progress, quest.maxProgress);
            const completed = newProgress >= quest.maxProgress;
            return {
              ...quest,
              progress: newProgress,
              completed,
              requirements: quest.requirements.map(req => ({
                ...req,
                current: newProgress
              }))
            };
          }
          return quest;
        })
      );
    },
    completeQuest: (questId: string) => {
      update(quests => 
        quests.map(quest => 
          quest.id === questId 
            ? { ...quest, completed: true, progress: quest.maxProgress }
            : quest
        )
      );
    },
    resetDailyQuests: () => {
      update(quests => 
        quests.map(quest => 
          quest.type === 'daily' 
            ? { 
                ...quest, 
                completed: false, 
                progress: 0,
                requirements: quest.requirements.map(req => ({ ...req, current: 0 }))
              }
            : quest
        )
      );
    },
    resetWeeklyQuests: () => {
      update(quests => 
        quests.map(quest => 
          quest.type === 'weekly' 
            ? { 
                ...quest, 
                completed: false, 
                progress: 0,
                requirements: quest.requirements.map(req => ({ ...req, current: 0 }))
              }
            : quest
        )
      );
    }
  };
}

export const questStore = createQuestStore();

// Derived stores
export const activeQuests = derived(
  questStore,
  $quests => $quests.filter(quest => !quest.completed)
);

export const completedQuests = derived(
  questStore,
  $quests => $quests.filter(quest => quest.completed)
);

export const dailyQuests = derived(
  questStore,
  $quests => $quests.filter(quest => quest.type === 'daily')
);

export const weeklyQuests = derived(
  questStore,
  $quests => $quests.filter(quest => quest.type === 'weekly')
);

export const achievementQuests = derived(
  questStore,
  $quests => $quests.filter(quest => quest.type === 'achievement')
);
