import { test, expect } from '@playwright/test';

test.describe('Game Canvas Interaction', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        // Wait for the page to load
        await page.waitForLoadState('networkidle');
    });

    test('should show game overlay initially', async ({ page }) => {
        // Check that the game overlay is visible
        const overlay = page.locator('.game-overlay');
        await expect(overlay).toBeVisible();

        // Check that the focus hint is visible
        const focusHint = page.locator('.focus-hint');
        await expect(focusHint).toBeVisible();
        await expect(focusHint).toContainText('Click anywhere to start playing');
    });

    test('should hide overlay when overlay is clicked', async ({ page }) => {
        // Wait for the game to initialize
        await page.waitForTimeout(2000);

        // Check initial state - overlay should be visible
        const overlay = page.locator('.game-overlay');
        await expect(overlay).toBeVisible();

        // Click on the overlay itself (which should now have a click handler)
        await overlay.click();

        // Wait a bit for the state to update
        await page.waitForTimeout(500);

        // Check if overlay is hidden
        await expect(overlay).toBeHidden();
    });



    test('should test toggle buttons functionality', async ({ page }) => {
        await page.waitForTimeout(2000);

        // Test task form toggle
        const taskFormButton = page.locator('button:has-text("Add Task")');
        await expect(taskFormButton).toBeVisible();

        await taskFormButton.click();

        // Check if task form overlay appears
        const taskFormOverlay = page.locator('.task-form-overlay');
        await expect(taskFormOverlay).toBeVisible();

        // Close the overlay
        const closeButton = page.locator('.close-btn').first();
        await closeButton.click();
        await expect(taskFormOverlay).toBeHidden();

        // Test task list toggle
        const taskListButton = page.locator('button:has-text("Tasks")');
        await taskListButton.click();

        const taskListOverlay = page.locator('.task-list-overlay');
        await expect(taskListOverlay).toBeVisible();
    });

    test('should dismiss overlays with close button', async ({ page }) => {
        await page.waitForTimeout(2000);

        // Test task form overlay dismissal with close button
        const taskFormButton = page.locator('button:has-text("Add Task")');
        await taskFormButton.click();

        const taskFormOverlay = page.locator('.task-form-overlay');
        await expect(taskFormOverlay).toBeVisible();

        // Click close button to close
        const closeButton = page.locator('.close-btn').first();
        await closeButton.click();
        await expect(taskFormOverlay).toBeHidden();

        // Test task list overlay dismissal with close button
        const taskListButton = page.locator('button:has-text("Tasks")');
        await taskListButton.click();

        const taskListOverlay = page.locator('.task-list-overlay');
        await expect(taskListOverlay).toBeVisible();

        // Click close button to close
        const taskListCloseButton = page.locator('.task-list-overlay .close-btn');
        await taskListCloseButton.click();
        await expect(taskListOverlay).toBeHidden();
    });
});
