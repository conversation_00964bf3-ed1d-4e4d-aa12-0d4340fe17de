import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock PixiJS for banana animation testing
vi.mock('pixi.js', () => {
    return {
        Application: vi.fn(() => ({
            stage: {
                addChild: vi.fn(),
                removeChild: vi.fn(),
            },
            ticker: {
                add: vi.fn(),
                remove: vi.fn(),
            },
            destroy: vi.fn(),
        })),
        AnimatedSprite: vi.fn(() => ({
            scale: { x: 1, y: 1, set: vi.fn() },
            x: 0,
            y: 0,
            animationSpeed: 0.1,
            loop: true,
            playing: false,
            play: vi.fn(),
            stop: vi.fn(),
            gotoAndStop: vi.fn(),
            visible: true,
            width: 32,
            height: 32,
        })),
        Assets: {
            load: vi.fn().mockResolvedValue({ source: { width: 192, height: 16 } }),
        },
        Texture: {
            WHITE: { source: { width: 16, height: 16 } },
            constructor: vi.fn(() => ({ source: { width: 16, height: 16 } })),
        },
        Rectangle: vi.fn(),
        Sprite: vi.fn(() => ({
            width: 32,
            height: 32,
            tint: 0x654321,
            x: 0,
            y: 0,
        })),
        Container: vi.fn(() => ({
            addChild: vi.fn(),
            removeChildren: vi.fn(),
        })),
    };
});

describe('GameCanvas Banana Animation System', () => {
    let mockBananaFrames: any[];
    let mockBananas: any[];
    let createBananas: () => void;

    beforeEach(async () => {
        vi.clearAllMocks();

        // Setup mock banana frames (12 frames, 16x16 each)
        const mockTexture = { source: { width: 192, height: 16 } };
        mockBananaFrames = Array(12).fill(mockTexture);
        mockBananas = [];

        // Import PIXI to get the mocked version
        const PIXI = await import('pixi.js');

        // Mock createBananas function
        createBananas = () => {
            if (mockBananaFrames.length === 0) {
                console.warn('Banana frames not loaded yet, skipping banana creation');
                return;
            }

            const bananaPositions = [
                { x: 200, y: 450 },
                { x: 400, y: 350 },
                { x: 600, y: 400 },
                { x: 300, y: 300 }
            ];

            bananaPositions.forEach(pos => {
                const banana = new PIXI.AnimatedSprite(mockBananaFrames);
                banana.animationSpeed = 0.1;
                banana.loop = true;
                banana.play();
                banana.scale.set(2, 2);
                banana.x = pos.x;
                banana.y = pos.y;
                mockBananas.push(banana);
            });
        };
    });

    it('should create animated banana sprites with correct properties', () => {
        createBananas();

        expect(mockBananas).toHaveLength(4);
        
        mockBananas.forEach(banana => {
            expect(banana.animationSpeed).toBe(0.1);
            expect(banana.loop).toBe(true);
            expect(banana.play).toHaveBeenCalled();
            expect(banana.scale.set).toHaveBeenCalledWith(2, 2);
        });
    });

    it('should position bananas at correct locations', () => {
        createBananas();

        const expectedPositions = [
            { x: 200, y: 450 },
            { x: 400, y: 350 },
            { x: 600, y: 400 },
            { x: 300, y: 300 }
        ];

        mockBananas.forEach((banana, index) => {
            expect(banana.x).toBe(expectedPositions[index].x);
            expect(banana.y).toBe(expectedPositions[index].y);
        });
    });

    it('should not create bananas if frames are not loaded', () => {
        mockBananaFrames.length = 0; // Simulate frames not loaded
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        createBananas();

        expect(mockBananas).toHaveLength(0);
        expect(consoleSpy).toHaveBeenCalledWith('Banana frames not loaded yet, skipping banana creation');
        
        consoleSpy.mockRestore();
    });

    it('should create bananas with 2x scaling for 16x16 sprites', () => {
        createBananas();

        mockBananas.forEach(banana => {
            expect(banana.scale.set).toHaveBeenCalledWith(2, 2);
        });
    });

    it('should set correct animation properties for banana sprites', () => {
        createBananas();

        mockBananas.forEach(banana => {
            expect(banana.animationSpeed).toBe(0.1); // Slower than monkey animations
            expect(banana.loop).toBe(true);
            expect(banana.play).toHaveBeenCalled();
        });
    });
});
