<script lang="ts">
  import { taskStore } from '$lib/stores';
  import TaskItem from './TaskItem.svelte';
  import type { Task } from '$lib/types';

  // Subscribe to tasks
  $: tasks = $taskStore;
  $: pendingTasks = tasks.filter((task: Task) => !task.completed);
  $: completedTasks = tasks.filter((task: Task) => task.completed);
  
  // Filter and sort options
  let showCompleted = false;
  let sortBy: 'created' | 'priority' | 'dueDate' | 'title' = 'created';
  let filterCategory = '';
  
  // Get unique categories
  $: categories = [...new Set(tasks.map((task: Task) => task.category).filter(Boolean))];

  // Apply filters and sorting
  $: filteredTasks = (() => {
    let filtered = showCompleted ? tasks : pendingTasks;

    // Filter by category
    if (filterCategory) {
      filtered = filtered.filter((task: Task) => task.category === filterCategory);
    }

    // Sort tasks
    return filtered.sort((a: Task, b: Task) => {
      switch (sortBy) {
        case 'priority': {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'created':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });
  })();
  
  function clearCompleted() {
    completedTasks.forEach((task: Task) => {
      taskStore.delete(task.id);
    });
  }
</script>

<div class="task-list-container">
  <div class="task-list-header">
    <h3>
      Tasks 
      <span class="task-count">
        ({pendingTasks.length} pending{completedTasks.length > 0 ? `, ${completedTasks.length} completed` : ''})
      </span>
    </h3>
    
    <div class="task-controls">
      <div class="control-group">
        <label for="sortBy">Sort by:</label>
        <select id="sortBy" bind:value={sortBy}>
          <option value="created">Date Created</option>
          <option value="priority">Priority</option>
          <option value="dueDate">Due Date</option>
          <option value="title">Title</option>
        </select>
      </div>
      
      {#if categories.length > 0}
        <div class="control-group">
          <label for="filterCategory">Category:</label>
          <select id="filterCategory" bind:value={filterCategory}>
            <option value="">All Categories</option>
            {#each categories as category (category)}
              <option value={category}>{category}</option>
            {/each}
          </select>
        </div>
      {/if}
      
      <div class="control-group">
        <label class="checkbox-label">
          <input type="checkbox" bind:checked={showCompleted} />
          Show completed
        </label>
      </div>
      
      {#if completedTasks.length > 0}
        <button class="clear-btn" on:click={clearCompleted}>
          Clear Completed
        </button>
      {/if}
    </div>
  </div>
  
  <div class="task-list">
    {#if filteredTasks.length === 0}
      <div class="empty-state">
        {#if tasks.length === 0}
          <p>🎯 No tasks yet! Add your first task above to get started.</p>
        {:else if filterCategory}
          <p>📂 No tasks found in "{filterCategory}" category.</p>
        {:else if showCompleted}
          <p>✅ No completed tasks to show.</p>
        {:else}
          <p>🎉 All tasks completed! Great job!</p>
        {/if}
      </div>
    {:else}
      {#each filteredTasks as task (task.id)}
        <TaskItem {task} />
      {/each}
    {/if}
  </div>
</div>

<style>
  .task-list-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid #E5E7EB;
    overflow: hidden;
  }
  
  .task-list-header {
    padding: 1.5rem;
    border-bottom: 2px solid #E5E7EB;
    background: #F9FAFB;
  }
  
  .task-list-header h3 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1.25rem;
  }
  
  .task-count {
    font-weight: normal;
    color: #6B7280;
    font-size: 0.9rem;
  }
  
  .task-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
  }
  
  .control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .control-group label {
    font-size: 0.9rem;
    color: #374151;
    font-weight: 500;
  }
  
  .control-group select {
    padding: 0.5rem;
    border: 1px solid #D1D5DB;
    border-radius: 6px;
    font-size: 0.9rem;
    background: white;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #374151;
  }
  
  .checkbox-label input[type="checkbox"] {
    margin: 0;
  }
  
  .clear-btn {
    background: #EF4444;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .clear-btn:hover {
    background: #DC2626;
  }
  
  .task-list {
    max-height: 500px;
    overflow-y: auto;
  }
  
  .empty-state {
    padding: 3rem 1.5rem;
    text-align: center;
    color: #6B7280;
    font-style: italic;
  }
  
  .empty-state p {
    margin: 0;
    font-size: 1.1rem;
  }
  
  @media (max-width: 768px) {
    .task-controls {
      flex-direction: column;
      align-items: stretch;
    }
    
    .control-group {
      justify-content: space-between;
    }
    
    .task-list-header {
      padding: 1rem;
    }
    
    .empty-state {
      padding: 2rem 1rem;
    }
  }
</style>
