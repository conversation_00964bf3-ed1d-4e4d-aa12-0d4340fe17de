import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { settingsStore } from './settingsStore';


describe('settingsStore', () => {
    beforeEach(() => {
        // Reset store to default state
        settingsStore.set({
            soundEnabled: true,
            musicEnabled: true,
            retroFilter: false,
            highContrast: false,
            animationsEnabled: true
        });
    });

    describe('initial state', () => {
        it('should have correct default settings', () => {
            const settings = get(settingsStore);
            expect(settings.soundEnabled).toBe(true);
            expect(settings.musicEnabled).toBe(true);
            expect(settings.retroFilter).toBe(false);
            expect(settings.highContrast).toBe(false);
            expect(settings.animationsEnabled).toBe(true);
        });
    });

    describe('toggleSound', () => {
        it('should toggle sound from true to false', () => {
            settingsStore.toggleSound();
            const settings = get(settingsStore);
            expect(settings.soundEnabled).toBe(false);
        });

        it('should toggle sound from false to true', () => {
            // First set to false
            settingsStore.set({
                soundEnabled: false,
                musicEnabled: true,
                retroFilter: false,
                highContrast: false,
                animationsEnabled: true
            });

            settingsStore.toggleSound();
            const settings = get(settingsStore);
            expect(settings.soundEnabled).toBe(true);
        });

        it('should preserve other settings when toggling sound', () => {
            const initialSettings = get(settingsStore);
            settingsStore.toggleSound();
            const newSettings = get(settingsStore);

            expect(newSettings.musicEnabled).toBe(initialSettings.musicEnabled);
            expect(newSettings.retroFilter).toBe(initialSettings.retroFilter);
            expect(newSettings.highContrast).toBe(initialSettings.highContrast);
            expect(newSettings.animationsEnabled).toBe(initialSettings.animationsEnabled);
        });
    });

    describe('toggleMusic', () => {
        it('should toggle music from true to false', () => {
            settingsStore.toggleMusic();
            const settings = get(settingsStore);
            expect(settings.musicEnabled).toBe(false);
        });

        it('should toggle music from false to true', () => {
            // First set to false
            settingsStore.set({
                soundEnabled: true,
                musicEnabled: false,
                retroFilter: false,
                highContrast: false,
                animationsEnabled: true
            });

            settingsStore.toggleMusic();
            const settings = get(settingsStore);
            expect(settings.musicEnabled).toBe(true);
        });

        it('should preserve other settings when toggling music', () => {
            const initialSettings = get(settingsStore);
            settingsStore.toggleMusic();
            const newSettings = get(settingsStore);

            expect(newSettings.soundEnabled).toBe(initialSettings.soundEnabled);
            expect(newSettings.retroFilter).toBe(initialSettings.retroFilter);
            expect(newSettings.highContrast).toBe(initialSettings.highContrast);
            expect(newSettings.animationsEnabled).toBe(initialSettings.animationsEnabled);
        });
    });

    describe('toggleRetroFilter', () => {
        it('should toggle retro filter from false to true', () => {
            settingsStore.toggleRetroFilter();
            const settings = get(settingsStore);
            expect(settings.retroFilter).toBe(true);
        });

        it('should toggle retro filter from true to false', () => {
            // First set to true
            settingsStore.set({
                soundEnabled: true,
                musicEnabled: true,
                retroFilter: true,
                highContrast: false,
                animationsEnabled: true
            });

            settingsStore.toggleRetroFilter();
            const settings = get(settingsStore);
            expect(settings.retroFilter).toBe(false);
        });
    });

    describe('toggleHighContrast', () => {
        it('should toggle high contrast from false to true', () => {
            settingsStore.toggleHighContrast();
            const settings = get(settingsStore);
            expect(settings.highContrast).toBe(true);
        });

        it('should toggle high contrast from true to false', () => {
            // First set to true
            settingsStore.set({
                soundEnabled: true,
                musicEnabled: true,
                retroFilter: false,
                highContrast: true,
                animationsEnabled: true
            });

            settingsStore.toggleHighContrast();
            const settings = get(settingsStore);
            expect(settings.highContrast).toBe(false);
        });
    });

    describe('toggleAnimations', () => {
        it('should toggle animations from true to false', () => {
            settingsStore.toggleAnimations();
            const settings = get(settingsStore);
            expect(settings.animationsEnabled).toBe(false);
        });

        it('should toggle animations from false to true', () => {
            // First set to false
            settingsStore.set({
                soundEnabled: true,
                musicEnabled: true,
                retroFilter: false,
                highContrast: false,
                animationsEnabled: false
            });

            settingsStore.toggleAnimations();
            const settings = get(settingsStore);
            expect(settings.animationsEnabled).toBe(true);
        });
    });

    describe('updateSetting', () => {
        it('should update individual setting by key', () => {
            settingsStore.updateSetting('soundEnabled', false);
            const settings = get(settingsStore);
            expect(settings.soundEnabled).toBe(false);
        });

        it('should update multiple settings individually', () => {
            settingsStore.updateSetting('retroFilter', true);
            settingsStore.updateSetting('highContrast', true);
            settingsStore.updateSetting('musicEnabled', false);

            const settings = get(settingsStore);
            expect(settings.retroFilter).toBe(true);
            expect(settings.highContrast).toBe(true);
            expect(settings.musicEnabled).toBe(false);
            expect(settings.soundEnabled).toBe(true); // Should remain unchanged
            expect(settings.animationsEnabled).toBe(true); // Should remain unchanged
        });

        it('should preserve other settings when updating one', () => {
            const initialSettings = get(settingsStore);
            settingsStore.updateSetting('retroFilter', true);
            const newSettings = get(settingsStore);

            expect(newSettings.soundEnabled).toBe(initialSettings.soundEnabled);
            expect(newSettings.musicEnabled).toBe(initialSettings.musicEnabled);
            expect(newSettings.highContrast).toBe(initialSettings.highContrast);
            expect(newSettings.animationsEnabled).toBe(initialSettings.animationsEnabled);
            expect(newSettings.retroFilter).toBe(true); // Only this should change
        });
    });

    describe('complex scenarios', () => {
        it('should handle multiple toggle operations', () => {
            // Perform multiple toggles
            settingsStore.toggleSound();
            settingsStore.toggleMusic();
            settingsStore.toggleRetroFilter();
            settingsStore.toggleHighContrast();
            settingsStore.toggleAnimations();

            const settings = get(settingsStore);
            expect(settings.soundEnabled).toBe(false);
            expect(settings.musicEnabled).toBe(false);
            expect(settings.retroFilter).toBe(true);
            expect(settings.highContrast).toBe(true);
            expect(settings.animationsEnabled).toBe(false);
        });

        it('should handle mix of toggles and direct updates', () => {
            settingsStore.toggleSound(); // false
            settingsStore.updateSetting('musicEnabled', false);
            settingsStore.toggleRetroFilter(); // true
            settingsStore.updateSetting('highContrast', true);

            const settings = get(settingsStore);
            expect(settings.soundEnabled).toBe(false);
            expect(settings.musicEnabled).toBe(false);
            expect(settings.retroFilter).toBe(true);
            expect(settings.highContrast).toBe(true);
            expect(settings.animationsEnabled).toBe(true); // Should remain default
        });
    });
});
