import type { Task, Quest, GameUpgrade } from '../types';

/**
 * Calculate banana rewards with various bonuses and multipliers
 */
export class BananaCalculator {
  private static instance: BananaCalculator;
  
  private constructor() {}
  
  static getInstance(): BananaCalculator {
    if (!BananaCalculator.instance) {
      BananaCalculator.instance = new BananaCalculator();
    }
    return BananaCalculator.instance;
  }
  
  /**
   * Calculate base banana reward for a task
   */
  calculateTaskReward(task: Omit<Task, 'id' | 'createdAt'>): number {
    let baseReward = 0;
    
    // Base reward by priority
    switch (task.priority) {
      case 'low':
        baseReward = 5;
        break;
      case 'medium':
        baseReward = 10;
        break;
      case 'high':
        baseReward = 15;
        break;
    }
    
    // Bonus for having description
    if (task.description && task.description.trim().length > 0) {
      baseReward += 2;
    }
    
    // Bonus for having due date (encourages planning)
    if (task.dueDate) {
      baseReward += 1;
    }
    
    // Bonus for categorization
    if (task.category && task.category.trim().length > 0) {
      baseReward += 1;
    }
    
    return baseReward;
  }
  
  /**
   * Apply multipliers based on user upgrades and streaks
   */
  applyMultipliers(
    baseReward: number, 
    upgrades: GameUpgrade[], 
    streakDays: number = 0
  ): number {
    let multiplier = 1;
    
    // Upgrade multipliers
    upgrades.forEach(upgrade => {
      if (upgrade.purchased) {
        switch (upgrade.effect) {
          case 'banana_boost_25':
            multiplier += 0.25;
            break;
          case 'banana_boost_50':
            multiplier += 0.5;
            break;
          case 'double_bananas':
            multiplier *= 2;
            break;
        }
      }
    });
    
    // Streak bonus (up to 50% bonus for 10+ day streak)
    if (streakDays > 0) {
      const streakBonus = Math.min(streakDays * 0.05, 0.5);
      multiplier += streakBonus;
    }
    
    return Math.floor(baseReward * multiplier);
  }
  
  /**
   * Calculate quest completion reward
   */
  calculateQuestReward(quest: Quest): number {
    let baseReward = quest.bananaReward;
    
    // Bonus for quest type
    switch (quest.type) {
      case 'daily':
        // No bonus for daily quests
        break;
      case 'weekly':
        baseReward *= 1.5; // 50% bonus for weekly commitment
        break;
      case 'achievement':
        baseReward *= 2; // 100% bonus for long-term achievements
        break;
    }
    
    return Math.floor(baseReward);
  }
  
  /**
   * Calculate cost for unlocking features
   */
  calculateFeatureUnlockCost(feature: string, userLevel: number = 1): number {
    const baseCosts: Record<string, number> = {
      'categories': 100,
      'due-dates': 200,
      'priority-tags': 300,
      'habit-tracking': 500,
      'analytics': 750,
      'export-options': 1000,
      'collaboration': 1500,
      'calendar-sync': 2000,
      'custom-themes': 2500
    };
    
    const baseCost = baseCosts[feature] || 100;
    
    // Slight increase based on user level (prevents easy farming)
    const levelMultiplier = 1 + (userLevel - 1) * 0.1;
    
    return Math.floor(baseCost * levelMultiplier);
  }
  
  /**
   * Calculate upgrade costs with scaling
   */
  calculateUpgradeCost(upgradeId: string, currentLevel: number = 0): number {
    const baseCosts: Record<string, number> = {
      'faster-monkey': 250,
      'banana-bots': 500,
      'double-jump': 750,
      'banana-magnet': 1000,
      'banana-boost-25': 1250,
      'banana-boost-50': 2000,
      'auto-complete': 3000
    };
    
    const baseCost = baseCosts[upgradeId] || 500;
    
    // Exponential scaling for multiple levels
    const scalingFactor = Math.pow(1.5, currentLevel);
    
    return Math.floor(baseCost * scalingFactor);
  }
  
  /**
   * Calculate daily banana income from passive upgrades
   */
  calculatePassiveIncome(upgrades: GameUpgrade[]): number {
    let dailyIncome = 0;
    
    upgrades.forEach(upgrade => {
      if (upgrade.purchased) {
        switch (upgrade.effect) {
          case 'auto_harvest_1':
            dailyIncome += 60; // 1 per minute = 60 per hour (assuming 1 hour active play)
            break;
          case 'auto_harvest_5':
            dailyIncome += 300;
            break;
          case 'banana_tree':
            dailyIncome += 100; // Passive banana tree
            break;
        }
      }
    });
    
    return dailyIncome;
  }
  
  /**
   * Calculate total bananas needed for next major unlock
   */
  calculateNextMilestone(currentBananas: number, unlockedFeatures: string[]): {
    feature: string;
    cost: number;
    remaining: number;
  } | null {
    const allFeatures = [
      { name: 'categories', cost: 100 },
      { name: 'due-dates', cost: 200 },
      { name: 'priority-tags', cost: 300 },
      { name: 'habit-tracking', cost: 500 },
      { name: 'analytics', cost: 750 },
      { name: 'export-options', cost: 1000 },
      { name: 'collaboration', cost: 1500 },
      { name: 'calendar-sync', cost: 2000 },
      { name: 'custom-themes', cost: 2500 }
    ];
    
    // Find next unaffordable feature
    const nextFeature = allFeatures.find(feature => 
      !unlockedFeatures.includes(feature.name) && feature.cost > currentBananas
    );
    
    if (!nextFeature) return null;
    
    return {
      feature: nextFeature.name,
      cost: nextFeature.cost,
      remaining: nextFeature.cost - currentBananas
    };
  }
}
