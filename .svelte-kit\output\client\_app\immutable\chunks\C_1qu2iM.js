import{w as L,a7 as V,a8 as $,a9 as O,aa as A,ab as C,ac as R,M as K,L as Q,N as X,ad as z,ae as x,af as H,H as ee,ag as te,ah as k,C as T,B as I,x as re,I as w,ai as ne,aj as ae,ak as se,al as oe,am as ie,an as ue,F as ce,p as le,b as p,ao as fe,m as _e,O as y,e as M,ap as de,r as pe,aq as E,d as be,ar as he,as as W,Z as ve,at as ge,a2 as ye,au as we,av as me,aw as Ee,ax as Se,a4 as Te}from"./QsgQHiSo.js";const Le=["touchstart","touchmove"];function Oe(e){return Le.includes(e)}function Ve(e){L&&V(e)!==null&&$(e)}let j=!1;function Ae(){j||(j=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function Y(e){var t=C,r=R;O(null),A(null);try{return e()}finally{O(t),A(r)}}function $e(e,t,r,a=r){e.addEventListener(t,()=>Y(r));const s=e.__on_r;s?e.__on_r=()=>{s(),a(!0)}:e.__on_r=()=>a(!0),Ae()}const Ce=new Set,B=new Set;function Me(e,t,r,a={}){function s(n){if(a.capture||m.call(t,n),!n.cancelBubble)return Y(()=>r==null?void 0:r.call(this,n))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?Q(()=>{t.addEventListener(e,s,a)}):t.addEventListener(e,s,a),s}function ze(e,t,r,a,s){var n={capture:a,passive:s},c=Me(e,t,r,n);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&K(()=>{t.removeEventListener(e,c,n)})}function m(e){var q;var t=this,r=t.ownerDocument,a=e.type,s=((q=e.composedPath)==null?void 0:q.call(e))||[],n=s[0]||e.target,c=0,i=e.__root;if(i){var l=s.indexOf(i);if(l!==-1&&(t===document||t===window)){e.__root=t;return}var o=s.indexOf(t);if(o===-1)return;l<=o&&(c=l)}if(n=s[c]||e.target,n!==t){X(e,"currentTarget",{configurable:!0,get(){return n||r}});var h=C,d=R;O(null),A(null);try{for(var u,f=[];n!==null;){var _=n.assignedSlot||n.parentNode||n.host||null;try{var b=n["__"+a];if(b!=null&&(!n.disabled||e.target===n))if(z(b)){var[N,...J]=b;N.apply(n,[e,...J])}else b.call(n,e)}catch(S){u?f.push(S):u=S}if(e.cancelBubble||_===t||_===null)break;n=_}if(u){for(let S of f)queueMicrotask(()=>{throw S});throw u}}finally{e.__root=t,delete e.currentTarget,O(h),A(d)}}}function We(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=r,e.nodeValue=r+"")}function U(e,t){return F(e,t)}function Ne(e,t){x(),t.intro=t.intro??!1;const r=t.target,a=L,s=w;try{for(var n=V(r);n&&(n.nodeType!==H||n.data!==ee);)n=te(n);if(!n)throw k;T(!0),I(n),re();const c=F(e,{...t,anchor:n});if(w===null||w.nodeType!==H||w.data!==ne)throw ae(),k;return T(!1),c}catch(c){if(c===k)return t.recover===!1&&se(),x(),$(r),T(!1),U(e,t);throw c}finally{T(a),I(s)}}const v=new Map;function F(e,{target:t,anchor:r,props:a={},events:s,context:n,intro:c=!0}){x();var i=new Set,l=d=>{for(var u=0;u<d.length;u++){var f=d[u];if(!i.has(f)){i.add(f);var _=Oe(f);t.addEventListener(f,m,{passive:_});var b=v.get(f);b===void 0?(document.addEventListener(f,m,{passive:_}),v.set(f,1)):v.set(f,b+1)}}};l(oe(Ce)),B.add(l);var o=void 0,h=ie(()=>{var d=r??t.appendChild(ue());return ce(()=>{if(n){le({});var u=p;u.c=n}s&&(a.$$events=s),L&&fe(d,null),o=e(d,a)||{},L&&(R.nodes_end=w),n&&_e()}),()=>{var _;for(var u of i){t.removeEventListener(u,m);var f=v.get(u);--f===0?(document.removeEventListener(u,m),v.delete(u)):v.set(u,f)}B.delete(l),d!==r&&((_=d.parentNode)==null||_.removeChild(d))}});return D.set(o,h),o}let D=new WeakMap;function ke(e,t){const r=D.get(e);return r?(D.delete(e),r(t)):Promise.resolve()}function Z(e,t,r){if(e==null)return t(void 0),r&&r(void 0),y;const a=M(()=>e.subscribe(t,r));return a.unsubscribe?()=>a.unsubscribe():a}const g=[];function xe(e,t){return{subscribe:De(e,t).subscribe}}function De(e,t=y){let r=null;const a=new Set;function s(i){if(de(e,i)&&(e=i,r)){const l=!g.length;for(const o of a)o[1](),g.push(o,e);if(l){for(let o=0;o<g.length;o+=2)g[o][0](g[o+1]);g.length=0}}}function n(i){s(i(e))}function c(i,l=y){const o=[i,l];return a.add(o),a.size===1&&(r=t(s,n)||y),i(e),()=>{a.delete(o),a.size===0&&r&&(r(),r=null)}}return{set:s,update:n,subscribe:c}}function Ye(e,t,r){const a=!Array.isArray(e),s=a?[e]:e;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const n=t.length<2;return xe(r,(c,i)=>{let l=!1;const o=[];let h=0,d=y;const u=()=>{if(h)return;d();const _=t(a?o[0]:o,c,i);n?c(_):d=typeof _=="function"?_:y},f=s.map((_,b)=>Z(_,N=>{o[b]=N,h&=~(1<<b),l&&u()},()=>{h|=1<<b}));return l=!0,u(),function(){pe(f),d(),l=!1}})}function Ue(e){let t;return Z(e,r=>t=r)(),t}function Re(){var e;return C===null&&he(),((e=C).ac??(e.ac=new AbortController)).signal}function G(e){p===null&&E(),ve&&p.l!==null?P(p).m.push(e):be(()=>{const t=M(e);if(typeof t=="function")return t})}function Pe(e){p===null&&E(),G(()=>()=>M(e))}function qe(e,t,{bubbles:r=!1,cancelable:a=!1}={}){return new CustomEvent(e,{detail:t,bubbles:r,cancelable:a})}function He(){const e=p;return e===null&&E(),(t,r,a)=>{var n;const s=(n=e.s.$$events)==null?void 0:n[t];if(s){const c=z(s)?s.slice():[s],i=qe(t,r,a);for(const l of c)l.call(e.x,i);return!i.defaultPrevented}return!0}}function Ie(e){p===null&&E(),p.l===null&&W(),P(p).b.push(e)}function je(e){p===null&&E(),p.l===null&&W(),P(p).a.push(e)}function P(e){var t=e.l;return t.u??(t.u={a:[],b:[],m:[]})}const Fe=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:je,beforeUpdate:Ie,createEventDispatcher:He,createRawSnippet:ge,flushSync:ye,getAbortSignal:Re,getAllContexts:we,getContext:me,hasContext:Ee,hydrate:Ne,mount:U,onDestroy:Pe,onMount:G,setContext:Se,tick:Te,unmount:ke,untrack:M},Symbol.toStringTag,{value:"Module"}));export{Z as a,Ae as b,Pe as c,Ye as d,ze as e,Fe as f,Ue as g,Ne as h,$e as l,U as m,G as o,Ve as r,We as s,ke as u,De as w};
