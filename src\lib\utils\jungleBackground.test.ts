import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock the shader module first
const mockShaderInstance = {
    mesh: {
        position: { set: vi.fn() },
        width: 800,
        height: 600,
        zIndex: -1000,
        destroy: vi.fn(),
    },
    update: vi.fn(),
    resize: vi.fn(),
    destroy: vi.fn(),
};

vi.mock('./jungleShader.js', () => ({
    createJungleShader: vi.fn(() => mockShaderInstance),
}));

// Mock PIXI.js with comprehensive support
vi.mock('pixi.js', () => {
    const mockContainer = {
        addChild: vi.fn(),
        addChildAt: vi.fn(),
        removeChild: vi.fn(),
        destroy: vi.fn(),
        zIndex: 0,
        sortableChildren: false,
    };

    const mockGraphics = {
        beginFill: vi.fn().mockReturnThis(),
        drawRect: vi.fn().mockReturnThis(),
        drawCircle: vi.fn().mockReturnThis(),
        drawEllipse: vi.fn().mockReturnThis(),
        endFill: vi.fn().mockReturnThis(),
        clear: vi.fn().mockReturnThis(),
        moveTo: vi.fn().mockReturnThis(),
        lineTo: vi.fn().mockReturnThis(),
        closePath: vi.fn().mockReturnThis(),
        alpha: 1,
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        zIndex: 0,
        scale: { set: vi.fn() },
        destroy: vi.fn(),
    };

    const MockContainer = vi.fn(() => ({ ...mockContainer }));
    const MockGraphics = vi.fn(() => ({ ...mockGraphics }));

    return {
        Container: MockContainer,
        Graphics: MockGraphics,
    };
});

// Import after mocks are set up
import { createJungleBackground } from './jungleBackground';



describe('Jungle Background System', () => {
    let mockApp: any;
    let consoleLogSpy: any;
    let consoleWarnSpy: any;

    beforeEach(() => {
        mockApp = {
            screen: {
                width: 800,
                height: 600,
            },
        };

        // Spy on console methods
        consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => { });
        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });
    });

    afterEach(() => {
        vi.clearAllMocks();
        consoleLogSpy.mockRestore();
        consoleWarnSpy.mockRestore();
    });

    describe('Background Creation', () => {
        it('should create background container with proper z-index', () => {
            const background = createJungleBackground(mockApp);

            expect(background).toBeDefined();
            expect(background.container).toBeDefined();
            expect(background.container.zIndex).toBe(-100);
            expect(background.container.sortableChildren).toBe(true);
        });

        it('should return all required methods', () => {
            const background = createJungleBackground(mockApp);

            expect(background.update).toBeDefined();
            expect(background.resize).toBeDefined();
            expect(background.initializeShader).toBeDefined();
            expect(background.destroy).toBeDefined();
            expect(typeof background.update).toBe('function');
            expect(typeof background.resize).toBe('function');
            expect(typeof background.initializeShader).toBe('function');
            expect(typeof background.destroy).toBe('function');
        });

        it('should create background layers without shader initially', () => {
            const background = createJungleBackground(mockApp);

            // Should have all required components
            expect(background.container).toBeDefined();
            expect(background.initializeShader).toBeDefined();
            expect(background.update).toBeDefined();
            expect(background.resize).toBeDefined();
            expect(background.destroy).toBeDefined();
        });
    });

    describe('Lazy Shader Initialization', () => {
        it('should initialize shader on first call to initializeShader', async () => {
            const { createJungleShader } = await import('./jungleShader.js');
            const background = createJungleBackground(mockApp);

            // Initially shader should not be created
            expect(createJungleShader).not.toHaveBeenCalled();

            // Call initializeShader
            background.initializeShader();

            // Now shader should be created
            expect(createJungleShader).toHaveBeenCalledWith(mockApp);
            expect(consoleLogSpy).toHaveBeenCalledWith('Jungle shader initialized successfully');
        });

        it('should not initialize shader multiple times', async () => {
            const { createJungleShader } = await import('./jungleShader.js');
            const background = createJungleBackground(mockApp);

            // Call initializeShader multiple times
            background.initializeShader();
            background.initializeShader();
            background.initializeShader();

            // Should only be called once
            expect(createJungleShader).toHaveBeenCalledTimes(1);
        });

        it('should handle shader initialization failure gracefully', async () => {
            const { createJungleShader } = await import('./jungleShader.js');

            // Mock shader creation to throw error
            (createJungleShader as any).mockImplementationOnce(() => {
                throw new Error('WebGL not supported');
            });

            const background = createJungleBackground(mockApp);
            background.initializeShader();

            expect(consoleWarnSpy).toHaveBeenCalledWith(
                'Failed to initialize jungle shader:',
                expect.any(Error)
            );
        });
    });

    describe('Animation Updates', () => {
        it('should update background animations without shader', () => {
            const background = createJungleBackground(mockApp);

            // Should not throw when updating without shader
            expect(() => background.update(16.67)).not.toThrow();
        });

        it('should update shader when initialized', async () => {
            const { createJungleShader } = await import('./jungleShader.js');
            const mockShaderInstance = {
                mesh: { zIndex: -1000, destroy: vi.fn() },
                update: vi.fn(),
                resize: vi.fn(),
                destroy: vi.fn(),
            };

            (createJungleShader as any).mockReturnValue(mockShaderInstance);

            const background = createJungleBackground(mockApp);
            background.initializeShader();
            background.update(16.67);

            expect(mockShaderInstance.update).toHaveBeenCalledWith(16.67);
        });
    });

    describe('Resize Handling', () => {
        it('should resize background elements', () => {
            const background = createJungleBackground(mockApp);

            // Change app screen size
            mockApp.screen.width = 1200;
            mockApp.screen.height = 800;

            // Should not throw when resizing
            expect(() => background.resize()).not.toThrow();
        });

        it('should resize shader when initialized', async () => {
            const { createJungleShader } = await import('./jungleShader.js');
            const mockShaderInstance = {
                mesh: { zIndex: -1000, destroy: vi.fn() },
                update: vi.fn(),
                resize: vi.fn(),
                destroy: vi.fn(),
            };

            (createJungleShader as any).mockReturnValue(mockShaderInstance);

            const background = createJungleBackground(mockApp);
            background.initializeShader();
            background.resize();

            expect(mockShaderInstance.resize).toHaveBeenCalled();
        });
    });

    describe('Cleanup', () => {
        it('should destroy background without shader', () => {
            const background = createJungleBackground(mockApp);

            expect(() => background.destroy()).not.toThrow();
            expect(background.container.destroy).toHaveBeenCalledWith({ children: true });
        });

        it('should destroy shader when initialized', async () => {
            const { createJungleShader } = await import('./jungleShader.js');
            const mockShaderInstance = {
                mesh: { zIndex: -1000, destroy: vi.fn() },
                update: vi.fn(),
                resize: vi.fn(),
                destroy: vi.fn(),
            };

            (createJungleShader as any).mockReturnValue(mockShaderInstance);

            const background = createJungleBackground(mockApp);
            background.initializeShader();
            background.destroy();

            expect(mockShaderInstance.destroy).toHaveBeenCalled();
            expect(background.container.destroy).toHaveBeenCalledWith({ children: true });
        });
    });
});
