

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export const universal = {
  "ssr": false
};
export const universal_id = "src/routes/+page.js";
export const imports = ["_app/immutable/nodes/2.DTKhn8Zg.js","_app/immutable/chunks/BgykbHj9.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/Eaidhwvc.js","_app/immutable/chunks/QsgQHiSo.js","_app/immutable/chunks/C_1qu2iM.js","_app/immutable/chunks/yuiQqa5R.js"];
export const stylesheets = ["_app/immutable/assets/2.q15tuB_a.css"];
export const fonts = [];
