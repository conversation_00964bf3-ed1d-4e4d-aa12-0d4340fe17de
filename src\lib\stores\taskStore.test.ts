import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { taskStore, goalStore } from './taskStore';
import type { Task, Goal } from '$lib/types';

describe('taskStore', () => {
    beforeEach(() => {
        // Reset stores
        taskStore.clear();
    });

    describe('add task', () => {
        it('should add a new task', () => {
            const newTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Test Task',
                description: 'Test Description',
                priority: 'medium',
                category: 'Test',
                completed: false,
                bananaReward: 10
            };

            taskStore.add(newTask);
            const tasks = get(taskStore);

            expect(tasks).toHaveLength(1);
            expect(tasks[0].title).toBe('Test Task');
            expect(tasks[0].id).toBeDefined();
            expect(tasks[0].createdAt).toBeDefined();
        });

        it('should generate unique IDs for multiple tasks', () => {
            const task1: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task 1',
                description: 'First task',
                priority: 'high',
                category: 'Work',
                completed: false,
                bananaReward: 15
            };

            const task2: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task 2',
                description: 'Second task',
                priority: 'low',
                category: 'Personal',
                completed: false,
                bananaReward: 5
            };

            taskStore.add(task1);
            taskStore.add(task2);

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(2);
            expect(tasks[0].id).not.toBe(tasks[1].id);
        });
    });

    describe('update task', () => {
        it('should update an existing task', () => {
            const newTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Original Task',
                description: 'Original Description',
                priority: 'medium',
                category: 'Test',
                completed: false,
                bananaReward: 10
            };

            const addedTask = taskStore.add(newTask);

            taskStore.update(addedTask.id, { title: 'Updated Task' });

            const tasks = get(taskStore);
            expect(tasks[0].title).toBe('Updated Task');
            expect(tasks[0].description).toBe('Original Description'); // Should remain unchanged
        });
    });

    describe('complete task', () => {
        it('should mark task as completed', () => {
            const newTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task to Complete',
                description: 'Test Description',
                priority: 'medium',
                category: 'Test',
                completed: false,
                bananaReward: 10
            };

            const addedTask = taskStore.add(newTask);
            taskStore.complete(addedTask.id);

            const tasks = get(taskStore);
            expect(tasks[0].completed).toBe(true);
            expect(tasks[0].completedAt).toBeDefined();
        });
    });

    describe('delete task', () => {
        it('should delete an existing task', () => {
            const newTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task to Delete',
                description: 'Test Description',
                priority: 'medium',
                category: 'Test',
                completed: false,
                bananaReward: 10
            };

            const addedTask = taskStore.add(newTask);
            expect(get(taskStore)).toHaveLength(1);

            taskStore.delete(addedTask.id);
            expect(get(taskStore)).toHaveLength(0);
        });
    });
});

describe('goalStore', () => {
    beforeEach(() => {
        // Reset stores
        goalStore.clear();
    });

    describe('add goal', () => {
        it('should add a new goal', () => {
            const newGoal: Omit<Goal, 'id' | 'createdAt'> = {
                title: 'Test Goal',
                description: 'Test Goal Description',
                milestones: [
                    {
                        id: 'milestone-1',
                        title: 'First milestone',
                        completed: false,
                        bananaReward: 25
                    },
                    {
                        id: 'milestone-2',
                        title: 'Second milestone',
                        completed: false,
                        bananaReward: 25
                    }
                ],
                category: 'productivity',
                totalBananaReward: 50
            };

            goalStore.add(newGoal);
            const goals = get(goalStore);

            expect(goals).toHaveLength(1);
            expect(goals[0].title).toBe('Test Goal');
            expect(goals[0].id).toBeDefined();
            expect(goals[0].createdAt).toBeDefined();
        });
    });
});
