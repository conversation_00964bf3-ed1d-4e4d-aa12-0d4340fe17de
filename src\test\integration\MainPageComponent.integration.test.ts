import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/svelte';
import { userStore, taskStore } from '$lib/stores';
import MainPageComponent from '$lib/components/MainPageComponent.svelte';

// Mock PixiJS to avoid canvas issues in tests
vi.mock('pixi.js', () => ({
    Application: vi.fn(() => ({
        view: { style: {} },
        stage: { addChild: vi.fn(), removeChild: vi.fn() },
        loader: { shared: { add: vi.fn().mockReturnThis(), load: vi.fn() } },
        ticker: { add: vi.fn(), remove: vi.fn() },
        destroy: vi.fn(),
        renderer: { resize: vi.fn() }
    })),
    Container: vi.fn(() => ({
        addChild: vi.fn(),
        removeChild: vi.fn(),
        position: { set: vi.fn() },
        scale: { set: vi.fn() }
    })),
    Sprite: vi.fn(() => ({
        anchor: { set: vi.fn() },
        position: { set: vi.fn() },
        scale: { set: vi.fn() },
        visible: true
    })),
    Texture: { from: vi.fn() },
    Graphics: vi.fn(() => ({
        beginFill: vi.fn().mockReturnThis(),
        drawRect: vi.fn().mockReturnThis(),
        endFill: vi.fn().mockReturnThis(),
        clear: vi.fn().mockReturnThis()
    }))
}));

describe('MainPageComponent Integration Tests', () => {
    beforeEach(() => {
        // Reset stores before each test
        userStore.set({
            id: 'test-user',
            bananaCount: 100,
            unlockedFeatures: [],
            isPremium: false,
            createdAt: new Date(),
            lastActiveAt: new Date()
        });
        taskStore.clear();
    });

    describe('Header and Navigation', () => {
        it('should render header with banana counter', async () => {
            render(MainPageComponent);

            expect(screen.getByText('🍌 Banana Checklist')).toBeInTheDocument();
            expect(screen.getByText('100')).toBeInTheDocument(); // banana count
        });

        it('should show toggle buttons for task management', async () => {
            render(MainPageComponent);

            expect(screen.getByText('➕ Add Task')).toBeInTheDocument();
            expect(screen.getByText('📋 Tasks')).toBeInTheDocument();
            expect(screen.getByText('🔧 Debug')).toBeInTheDocument();
        });

        it('should update banana count when userStore changes', async () => {
            render(MainPageComponent);

            // Initial count
            expect(screen.getByText('100')).toBeInTheDocument();

            // Update store
            userStore.addBananas(50);

            // Should show updated count
            expect(screen.getByText('150')).toBeInTheDocument();
        });
    });

    describe('Task Form Overlay', () => {
        it('should show task form when Add Task button is clicked', async () => {
            render(MainPageComponent);

            const addTaskButton = screen.getByText('➕ Add Task');
            await fireEvent.click(addTaskButton);

            expect(screen.getByText('➕ Add New Task')).toBeInTheDocument();
            expect(screen.getByRole('dialog')).toBeInTheDocument();
        });

        it('should close task form when close button is clicked', async () => {
            render(MainPageComponent);

            // Open task form
            const addTaskButton = screen.getByText('➕ Add Task');
            await fireEvent.click(addTaskButton);

            expect(screen.getByRole('dialog')).toBeInTheDocument();

            // Close task form
            const closeButton = screen.getByText('✕');
            await fireEvent.click(closeButton);

            expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
        });

        it('should close task form when overlay background is clicked', async () => {
            render(MainPageComponent);

            // Open task form
            const addTaskButton = screen.getByText('➕ Add Task');
            await fireEvent.click(addTaskButton);

            expect(screen.getByRole('dialog')).toBeInTheDocument();

            // Click overlay background
            const overlay = screen.getByRole('button', { name: '' }); // overlay div with role="button"
            await fireEvent.click(overlay);

            expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
        });

        it('should close task form when Escape key is pressed', async () => {
            render(MainPageComponent);

            // Open task form
            const addTaskButton = screen.getByText('➕ Add Task');
            await fireEvent.click(addTaskButton);

            expect(screen.getByRole('dialog')).toBeInTheDocument();

            // Press Escape
            const overlay = screen.getByRole('button', { name: '' });
            await fireEvent.keyDown(overlay, { key: 'Escape' });

            expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
        });
    });

    describe('Task List Overlay', () => {
        it('should show task list when Tasks button is clicked', async () => {
            render(MainPageComponent);

            const tasksButton = screen.getByText('📋 Tasks');
            await fireEvent.click(tasksButton);

            // Should show task list overlay (we'll need to check for specific task list content)
            expect(screen.getByRole('dialog')).toBeInTheDocument();
        });

        it('should close other overlays when opening task list', async () => {
            render(MainPageComponent);

            // Open task form first
            const addTaskButton = screen.getByText('➕ Add Task');
            await fireEvent.click(addTaskButton);
            expect(screen.getByText('➕ Add New Task')).toBeInTheDocument();

            // Open task list - should close task form
            const tasksButton = screen.getByText('📋 Tasks');
            await fireEvent.click(tasksButton);

            expect(screen.queryByText('➕ Add New Task')).not.toBeInTheDocument();
            expect(screen.getByRole('dialog')).toBeInTheDocument();
        });
    });

    describe('Debug Mode', () => {
        it('should toggle debug mode when debug button is clicked', async () => {
            render(MainPageComponent);

            const debugButton = screen.getByText('🔧 Debug');

            // Initially not active
            expect(debugButton).not.toHaveClass('active');

            // Click to activate
            await fireEvent.click(debugButton);
            expect(debugButton).toHaveClass('active');

            // Click again to deactivate
            await fireEvent.click(debugButton);
            expect(debugButton).not.toHaveClass('active');
        });

        it('should pass debug mode to GameCanvas component', async () => {
            render(MainPageComponent);

            const debugButton = screen.getByText('🔧 Debug');
            await fireEvent.click(debugButton);

            // The GameCanvas component should receive debugMode=true
            // This would be tested more thoroughly in GameCanvas component tests
            expect(debugButton).toHaveClass('active');
        });
    });

    describe('Button States', () => {
        it('should show active state for currently open overlay', async () => {
            render(MainPageComponent);

            const addTaskButton = screen.getByText('➕ Add Task');
            const tasksButton = screen.getByText('📋 Tasks');

            // Initially no active states
            expect(addTaskButton).not.toHaveClass('active');
            expect(tasksButton).not.toHaveClass('active');

            // Open task form
            await fireEvent.click(addTaskButton);
            expect(addTaskButton).toHaveClass('active');
            expect(tasksButton).not.toHaveClass('active');

            // Switch to task list
            await fireEvent.click(tasksButton);
            expect(addTaskButton).not.toHaveClass('active');
            expect(tasksButton).toHaveClass('active');
        });
    });
});
