import { test, expect } from '@playwright/test';

test.describe('Game Features E2E Tests', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        
        // Wait for the game to initialize
        await page.waitForTimeout(3000);
    });

    test.describe('Game Initialization', () => {
        test('should load game canvas and display initial elements', async ({ page }) => {
            // Check that the game canvas is present
            const canvas = page.locator('canvas');
            await expect(canvas).toBeVisible();
            
            // Check that the game overlay is initially visible
            const overlay = page.locator('.game-overlay');
            await expect(overlay).toBeVisible();
            
            // Check focus hint is displayed
            const focusHint = page.locator('.focus-hint');
            await expect(focusHint).toBeVisible();
            await expect(focusHint).toContainText('Click anywhere to start playing');
        });

        test('should display banana counter in header', async ({ page }) => {
            const bananaCounter = page.locator('.banana-counter');
            await expect(bananaCounter).toBeVisible();
            
            const bananaIcon = page.locator('.banana-icon');
            await expect(bananaIcon).toContainText('🍌');
            
            const bananaCount = page.locator('.banana-count');
            await expect(bananaCount).toBeVisible();
            
            // Should have a numeric value
            const countText = await bananaCount.textContent();
            expect(parseInt(countText || '0')).toBeGreaterThanOrEqual(0);
        });

        test('should show debug mode toggle', async ({ page }) => {
            const debugButton = page.locator('button:has-text("Debug")');
            await expect(debugButton).toBeVisible();
            await expect(debugButton).toHaveAttribute('title', 'Toggle Debug Mode (Show Collision Boxes)');
        });
    });

    test.describe('Game Interaction', () => {
        test('should start game when overlay is clicked', async ({ page }) => {
            const overlay = page.locator('.game-overlay');
            await expect(overlay).toBeVisible();
            
            // Click to start the game
            await overlay.click();
            
            // Wait for overlay to disappear
            await page.waitForTimeout(1000);
            await expect(overlay).toBeHidden();
            
            // Game should now be active (canvas should be interactive)
            const canvas = page.locator('canvas');
            await expect(canvas).toBeVisible();
        });

        test('should respond to keyboard controls', async ({ page }) => {
            // Start the game
            const overlay = page.locator('.game-overlay');
            if (await overlay.isVisible()) {
                await overlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Focus the canvas for keyboard input
            const canvas = page.locator('canvas');
            await canvas.click();
            
            // Test movement keys
            await page.keyboard.press('ArrowLeft');
            await page.waitForTimeout(100);
            await page.keyboard.press('ArrowRight');
            await page.waitForTimeout(100);
            await page.keyboard.press('Space'); // Jump
            await page.waitForTimeout(100);
            
            // The game should handle these inputs (we can't easily test the visual result,
            // but we can verify no errors occurred)
            const errorMessages = page.locator('.error, .exception');
            await expect(errorMessages).toHaveCount(0);
        });

        test('should toggle debug mode', async ({ page }) => {
            const debugButton = page.locator('button:has-text("Debug")');
            
            // Initially not active
            await expect(debugButton).not.toHaveClass(/active/);
            
            // Click to activate debug mode
            await debugButton.click();
            await expect(debugButton).toHaveClass(/active/);
            
            // Click again to deactivate
            await debugButton.click();
            await expect(debugButton).not.toHaveClass(/active/);
        });
    });

    test.describe('Banana Collection Simulation', () => {
        test('should simulate banana collection through task completion', async ({ page }) => {
            // Get initial banana count
            const bananaCountElement = page.locator('.banana-count');
            const initialCountText = await bananaCountElement.textContent();
            const initialCount = parseInt(initialCountText || '0');
            
            // Create and complete a task to earn bananas
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Banana Earning Task');
            await page.click('button:has-text("Add Task")');
            
            // Complete the task
            await page.click('button:has-text("Tasks")');
            const checkbox = page.locator('input[type="checkbox"]').first();
            await checkbox.check();
            
            // Wait for banana count to update
            await page.waitForTimeout(1000);
            
            // Verify banana count increased
            const finalCountText = await bananaCountElement.textContent();
            const finalCount = parseInt(finalCountText || '0');
            expect(finalCount).toBeGreaterThan(initialCount);
            
            // Close task list
            await page.click('.close-btn');
        });

        test('should maintain banana count across page interactions', async ({ page }) => {
            // Get initial count
            const bananaCountElement = page.locator('.banana-count');
            const initialCountText = await bananaCountElement.textContent();
            const initialCount = parseInt(initialCountText || '0');
            
            // Perform various interactions
            await page.click('button:has-text("Add Task")');
            await page.click('.close-btn');
            await page.click('button:has-text("Tasks")');
            await page.click('.close-btn');
            await page.click('button:has-text("Debug")');
            await page.click('button:has-text("Debug")');
            
            // Banana count should remain the same
            const finalCountText = await bananaCountElement.textContent();
            const finalCount = parseInt(finalCountText || '0');
            expect(finalCount).toBe(initialCount);
        });
    });

    test.describe('Game Performance', () => {
        test('should handle rapid interactions without errors', async ({ page }) => {
            // Start the game
            const overlay = page.locator('.game-overlay');
            if (await overlay.isVisible()) {
                await overlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Perform rapid interactions
            const canvas = page.locator('canvas');
            await canvas.click();
            
            // Rapid key presses
            for (let i = 0; i < 10; i++) {
                await page.keyboard.press('ArrowLeft');
                await page.keyboard.press('ArrowRight');
                await page.keyboard.press('Space');
                await page.waitForTimeout(50);
            }
            
            // Check for console errors
            const errors: string[] = [];
            page.on('console', msg => {
                if (msg.type() === 'error') {
                    errors.push(msg.text());
                }
            });
            
            await page.waitForTimeout(1000);
            
            // Should not have critical errors
            const criticalErrors = errors.filter(error => 
                !error.includes('favicon') && 
                !error.includes('404') &&
                !error.includes('net::ERR_')
            );
            expect(criticalErrors.length).toBe(0);
        });

        test('should maintain responsive UI during game interactions', async ({ page }) => {
            // Start the game
            const overlay = page.locator('.game-overlay');
            if (await overlay.isVisible()) {
                await overlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Interact with game while testing UI responsiveness
            const canvas = page.locator('canvas');
            await canvas.click();
            
            // Start some game input
            await page.keyboard.down('ArrowRight');
            
            // UI should still be responsive
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            
            await page.click('.close-btn');
            await expect(page.locator('.task-form-overlay')).toBeHidden();
            
            // Stop game input
            await page.keyboard.up('ArrowRight');
        });
    });

    test.describe('Game State Persistence', () => {
        test('should maintain game state when switching between overlays', async ({ page }) => {
            // Start the game
            const overlay = page.locator('.game-overlay');
            if (await overlay.isVisible()) {
                await overlay.click();
                await page.waitForTimeout(1000);
            }
            
            // Get initial banana count
            const bananaCountElement = page.locator('.banana-count');
            const initialCountText = await bananaCountElement.textContent();
            const initialCount = parseInt(initialCountText || '0');
            
            // Open and close various overlays
            await page.click('button:has-text("Add Task")');
            await page.waitForTimeout(500);
            await page.click('.close-btn');
            
            await page.click('button:has-text("Tasks")');
            await page.waitForTimeout(500);
            await page.click('.close-btn');
            
            // Banana count should be preserved
            const finalCountText = await bananaCountElement.textContent();
            const finalCount = parseInt(finalCountText || '0');
            expect(finalCount).toBe(initialCount);
            
            // Game canvas should still be visible and functional
            const canvas = page.locator('canvas');
            await expect(canvas).toBeVisible();
            await canvas.click(); // Should not cause errors
        });

        test('should handle debug mode state correctly', async ({ page }) => {
            const debugButton = page.locator('button:has-text("Debug")');
            
            // Enable debug mode
            await debugButton.click();
            await expect(debugButton).toHaveClass(/active/);
            
            // Open and close overlays
            await page.click('button:has-text("Add Task")');
            await page.click('.close-btn');
            
            // Debug mode should still be active
            await expect(debugButton).toHaveClass(/active/);
            
            // Disable debug mode
            await debugButton.click();
            await expect(debugButton).not.toHaveClass(/active/);
        });
    });

    test.describe('Visual Elements', () => {
        test('should display game title and branding', async ({ page }) => {
            const title = page.locator('h1:has-text("🍌 Banana Checklist")');
            await expect(title).toBeVisible();
        });

        test('should show proper button states and styling', async ({ page }) => {
            // Check button styling
            const addTaskButton = page.locator('button:has-text("Add Task")');
            const tasksButton = page.locator('button:has-text("Tasks")');
            const debugButton = page.locator('button:has-text("Debug")');
            
            await expect(addTaskButton).toBeVisible();
            await expect(tasksButton).toBeVisible();
            await expect(debugButton).toBeVisible();
            
            // Test active states
            await addTaskButton.click();
            await expect(addTaskButton).toHaveClass(/active/);
            
            await tasksButton.click();
            await expect(tasksButton).toHaveClass(/active/);
            await expect(addTaskButton).not.toHaveClass(/active/);
        });

        test('should handle responsive layout', async ({ page }) => {
            // Test different viewport sizes
            await page.setViewportSize({ width: 1200, height: 800 });
            await expect(page.locator('.fixed-header')).toBeVisible();
            await expect(page.locator('canvas')).toBeVisible();
            
            await page.setViewportSize({ width: 800, height: 600 });
            await expect(page.locator('.fixed-header')).toBeVisible();
            await expect(page.locator('canvas')).toBeVisible();
            
            await page.setViewportSize({ width: 400, height: 600 });
            await expect(page.locator('.fixed-header')).toBeVisible();
            await expect(page.locator('canvas')).toBeVisible();
        });
    });
});
