import * as PIXI from 'pixi.js';
import { createJungleShader } from './jungleShader.js';

interface FloatingParticle {
    sprite: PIXI.Graphics;
    baseX: number;
    baseY: number;
    speed: number;
    amplitude: number;
    phase: number;
    scale: number;
    alpha: number;
}

interface MovingCloud {
    sprite: PIXI.Graphics;
    speed: number;
    baseAlpha: number;
}

/**
 * Creates an enhanced animated jungle background with multiple layers and effects
 * @param app - The PIXI Application instance
 * @returns An object with the background graphics and update function
 */
export function createJungleBackground(app: PIXI.Application) {
    // Create a container for the background layers
    const backgroundContainer = new PIXI.Container();

    // Ensure background renders behind all game elements
    backgroundContainer.zIndex = -100;
    backgroundContainer.sortableChildren = true;

    // Shader will be initialized lazily after user interaction
    let atmosphericShader: ReturnType<typeof createJungleShader> | null = null;
    let shaderInitialized = false;

    // Create multiple depth layers with more complex colors
    const layers: PIXI.Graphics[] = [];
    const layerSpeeds = [0.1, 0.3, 0.6, 1.0, 1.5]; // More layers with varied speeds

    // Enhanced color palette with more depth
    const colors = [
        0x0a1f14, // Very dark forest green (far back)
        0x0f2a1a, // Dark forest green
        0x1a3d2e, // Medium forest green
        0x2b5a3e, // Lighter forest green
        0x3d6b4f  // Front layer with more brightness
    ];

    // Create background layers with gradient effects
    for (let i = 0; i < 5; i++) {
        const layer = new PIXI.Graphics();

        // Create a subtle vertical gradient for each layer
        layer.beginFill(colors[i]);
        layer.drawRect(0, 0, app.screen.width, app.screen.height);
        layer.endFill();

        // Add depth-based transparency and slight blur effect
        layer.alpha = 0.6 + (i * 0.08);

        layers.push(layer);
        backgroundContainer.addChild(layer);
    }

    // Create enhanced animated light rays with varying intensities
    const lightRays: PIXI.Graphics[] = [];
    const numRays = 12; // More rays for richer lighting
    const rayColors = [0x4a7c59, 0x5d8f6b, 0x6ba377, 0x7ab583]; // Varied light colors

    for (let i = 0; i < numRays; i++) {
        const ray = new PIXI.Graphics();
        const x = (app.screen.width / numRays) * i + Math.random() * 80;
        const width = 15 + Math.random() * 50;
        const color = rayColors[Math.floor(Math.random() * rayColors.length)];

        // Create a tapered light ray (wider at top, narrower at bottom)
        ray.beginFill(color);
        ray.moveTo(x, 0);
        ray.lineTo(x + width, 0);
        ray.lineTo(x + width * 0.3, app.screen.height);
        ray.lineTo(x - width * 0.2, app.screen.height);
        ray.closePath();
        ray.endFill();

        ray.alpha = 0.08 + Math.random() * 0.12;

        lightRays.push(ray);
        backgroundContainer.addChild(ray);
    }

    // Create floating particles (dust, pollen, small insects)
    const floatingParticles: FloatingParticle[] = [];
    const numParticles = 25;

    for (let i = 0; i < numParticles; i++) {
        const particle = new PIXI.Graphics();
        const size = 1 + Math.random() * 3;
        const particleColor = Math.random() > 0.7 ? 0xffd700 : 0x90ee90; // Gold or light green

        particle.beginFill(particleColor);
        particle.drawCircle(0, 0, size);
        particle.endFill();

        const floatingParticle: FloatingParticle = {
            sprite: particle,
            baseX: Math.random() * app.screen.width,
            baseY: Math.random() * app.screen.height,
            speed: 0.2 + Math.random() * 0.8,
            amplitude: 20 + Math.random() * 40,
            phase: Math.random() * Math.PI * 2,
            scale: 0.5 + Math.random() * 1.5,
            alpha: 0.3 + Math.random() * 0.4
        };

        particle.x = floatingParticle.baseX;
        particle.y = floatingParticle.baseY;
        particle.scale.set(floatingParticle.scale);
        particle.alpha = floatingParticle.alpha;

        floatingParticles.push(floatingParticle);
        backgroundContainer.addChild(particle);
    }

    // Create moving fog/mist clouds
    const movingClouds: MovingCloud[] = [];
    const numClouds = 6;

    for (let i = 0; i < numClouds; i++) {
        const cloud = new PIXI.Graphics();
        const cloudWidth = 100 + Math.random() * 200;
        const cloudHeight = 30 + Math.random() * 60;
        const y = Math.random() * app.screen.height;

        // Create an elliptical cloud shape
        cloud.beginFill(0x2d5a3f, 0.15); // Semi-transparent green mist
        cloud.drawEllipse(0, 0, cloudWidth, cloudHeight);
        cloud.endFill();

        const movingCloud: MovingCloud = {
            sprite: cloud,
            speed: 0.3 + Math.random() * 0.7,
            baseAlpha: 0.1 + Math.random() * 0.1
        };

        cloud.x = -cloudWidth + Math.random() * app.screen.width;
        cloud.y = y;
        cloud.alpha = movingCloud.baseAlpha;

        movingClouds.push(movingCloud);
        backgroundContainer.addChild(cloud);
    }

    // Animation variables
    let time = 0;

    // Function to initialize shader after user interaction
    function initializeShader() {
        if (!shaderInitialized) {
            try {
                atmosphericShader = createJungleShader(app);
                atmosphericShader.mesh.zIndex = -1000;
                backgroundContainer.addChildAt(atmosphericShader.mesh, 0); // Add at the back
                shaderInitialized = true;
                // eslint-disable-next-line no-console
                console.log('Jungle shader initialized successfully');
            } catch (error) {
                // eslint-disable-next-line no-console
                console.warn('Failed to initialize jungle shader:', error);
                shaderInitialized = true; // Mark as attempted to avoid retrying
            }
        }
    }

    // Enhanced animation function with multiple effects
    function updateBackground(deltaTime: number) {
        time += deltaTime * 0.01;

        // Update atmospheric shader if initialized
        if (atmosphericShader) {
            atmosphericShader.update(deltaTime);
        }

        // Animate the background layers with more complex movement
        layers.forEach((layer, index) => {
            const speed = layerSpeeds[index];
            const offset = Math.sin(time * speed) * (3 + index);
            const verticalOffset = Math.cos(time * speed * 0.7) * 1;

            layer.x = offset;
            layer.y = verticalOffset;

            // Enhanced alpha pulsing with breathing effect
            const baseAlpha = 0.6 + (index * 0.08);
            const pulse = Math.sin(time * speed * 1.5) * 0.06;
            const breathe = Math.sin(time * 0.3) * 0.03;
            layer.alpha = baseAlpha + pulse + breathe;
        });

        // Animate light rays with more dynamic movement
        lightRays.forEach((ray, index) => {
            const speed = 0.4 + (index * 0.1);
            const intensity = Math.sin(time * speed + index) * 0.1;
            const flicker = Math.sin(time * speed * 3 + index * 2) * 0.03;

            ray.alpha = 0.08 + intensity + flicker;

            // More pronounced horizontal movement with sway
            const baseX = (app.screen.width / numRays) * index;
            const sway = Math.sin(time * speed * 0.6) * 15;
            const drift = Math.cos(time * speed * 0.3) * 5;
            ray.x = baseX + sway + drift;
        });

        // Animate floating particles with complex motion
        floatingParticles.forEach((particle, index) => {
            particle.phase += particle.speed * deltaTime * 0.02;

            // Floating motion with wind effect
            const windX = Math.sin(particle.phase) * particle.amplitude;
            const windY = Math.cos(particle.phase * 0.7) * (particle.amplitude * 0.3);
            const drift = Math.sin(time * 0.2 + index) * 20;

            particle.sprite.x = particle.baseX + windX + drift;
            particle.sprite.y = particle.baseY + windY + Math.sin(time * 0.5) * 10;

            // Twinkling effect
            const twinkle = Math.sin(time * 2 + index * 0.5) * 0.2;
            particle.sprite.alpha = particle.alpha + twinkle;

            // Wrap particles around screen
            if (particle.sprite.x > app.screen.width + 50) {
                particle.baseX = -50;
            } else if (particle.sprite.x < -50) {
                particle.baseX = app.screen.width + 50;
            }
        });

        // Animate moving clouds
        movingClouds.forEach((cloudData, index) => {
            cloudData.sprite.x += cloudData.speed;

            // Vertical bobbing motion
            const bob = Math.sin(time * 0.5 + index) * 2;
            cloudData.sprite.y += bob * 0.1;

            // Alpha pulsing for mist effect
            const pulse = Math.sin(time * 0.8 + index * 0.3) * 0.05;
            cloudData.sprite.alpha = cloudData.baseAlpha + pulse;

            // Reset cloud position when it moves off screen
            if (cloudData.sprite.x > app.screen.width + 200) {
                cloudData.sprite.x = -200;
                cloudData.sprite.y = Math.random() * app.screen.height;
            }
        });
    }

    // Enhanced resize function to handle all elements
    function handleResize() {
        const newWidth = app.screen.width;
        const newHeight = app.screen.height;

        // Resize atmospheric shader if initialized
        if (atmosphericShader) {
            atmosphericShader.resize();
        }

        // Resize all background layers
        layers.forEach((layer, index) => {
            layer.clear();
            layer.beginFill(colors[index]);
            layer.drawRect(0, 0, newWidth, newHeight);
            layer.endFill();
        });

        // Recreate light rays with new dimensions
        lightRays.forEach((ray, index) => {
            const x = (newWidth / numRays) * index + Math.random() * 80;
            const width = 15 + Math.random() * 50;
            const color = rayColors[Math.floor(Math.random() * rayColors.length)];

            ray.clear();
            ray.beginFill(color);
            ray.moveTo(x, 0);
            ray.lineTo(x + width, 0);
            ray.lineTo(x + width * 0.3, newHeight);
            ray.lineTo(x - width * 0.2, newHeight);
            ray.closePath();
            ray.endFill();
        });

        // Reposition floating particles within new bounds
        floatingParticles.forEach(particle => {
            if (particle.baseX > newWidth) {
                particle.baseX = Math.random() * newWidth;
            }
            if (particle.baseY > newHeight) {
                particle.baseY = Math.random() * newHeight;
            }
        });

        // Reposition clouds within new bounds
        movingClouds.forEach(cloudData => {
            if (cloudData.sprite.y > newHeight) {
                cloudData.sprite.y = Math.random() * newHeight;
            }
        });
    }

    return {
        container: backgroundContainer,
        update: updateBackground,
        resize: handleResize,
        initializeShader,
        destroy: () => {
            if (atmosphericShader) {
                atmosphericShader.destroy();
            }
            backgroundContainer.destroy({ children: true });
        }
    };
}
