import { test, expect } from '@playwright/test';

test.describe('Task Management E2E Tests', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/');
        await page.waitForLoadState('networkidle');
        
        // Wait for the game to initialize
        await page.waitForTimeout(2000);
        
        // Dismiss the game overlay if present
        const overlay = page.locator('.game-overlay');
        if (await overlay.isVisible()) {
            await overlay.click();
            await page.waitForTimeout(500);
        }
    });

    test.describe('Task Creation Workflow', () => {
        test('should create a new task through the complete workflow', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            
            // Fill out the form
            await page.fill('input[placeholder*="task title"]', 'E2E Test Task');
            await page.fill('textarea[placeholder*="description"]', 'This task was created by an E2E test');
            await page.selectOption('select[name="category"]', 'work');
            await page.selectOption('select[name="priority"]', 'high');
            
            // Submit the form
            await page.click('button:has-text("Add Task")');
            
            // Verify form closes
            await expect(page.locator('.task-form-overlay')).toBeHidden();
            
            // Open task list to verify task was created
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('.task-list-overlay')).toBeVisible();
            
            // Verify task appears in list
            await expect(page.locator('text=E2E Test Task')).toBeVisible();
            await expect(page.locator('text=This task was created by an E2E test')).toBeVisible();
            await expect(page.locator('text=work')).toBeVisible();
            await expect(page.locator('text=high')).toBeVisible();
        });

        test('should validate required fields', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            
            // Try to submit without title
            await page.click('button:has-text("Add Task")');
            
            // Form should still be visible (validation failed)
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            
            // Check that the title field is marked as invalid
            const titleInput = page.locator('input[placeholder*="task title"]');
            await expect(titleInput).toHaveAttribute('required');
        });

        test('should clear form after successful submission', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            
            // Fill and submit first task
            await page.fill('input[placeholder*="task title"]', 'First Task');
            await page.fill('textarea[placeholder*="description"]', 'First description');
            await page.click('button:has-text("Add Task")');
            
            // Open form again
            await page.click('button:has-text("Add Task")');
            
            // Verify form is cleared
            await expect(page.locator('input[placeholder*="task title"]')).toHaveValue('');
            await expect(page.locator('textarea[placeholder*="description"]')).toHaveValue('');
        });
    });

    test.describe('Task Completion Workflow', () => {
        test('should complete a task and award bananas', async ({ page }) => {
            // Create a task first
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Completable Task');
            await page.click('button:has-text("Add Task")');
            
            // Check initial banana count
            const initialBananaText = await page.locator('.banana-count').textContent();
            const initialBananas = parseInt(initialBananaText || '0');
            
            // Open task list
            await page.click('button:has-text("Tasks")');
            
            // Complete the task
            const checkbox = page.locator('input[type="checkbox"]').first();
            await checkbox.check();
            
            // Wait for banana count to update
            await page.waitForTimeout(1000);
            
            // Verify banana count increased
            const finalBananaText = await page.locator('.banana-count').textContent();
            const finalBananas = parseInt(finalBananaText || '0');
            expect(finalBananas).toBeGreaterThan(initialBananas);
            
            // Verify task appears as completed
            await expect(checkbox).toBeChecked();
            const taskItem = page.locator('.task-item').first();
            await expect(taskItem).toHaveClass(/completed/);
        });

        test('should allow unchecking completed tasks', async ({ page }) => {
            // Create and complete a task
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Toggle Task');
            await page.click('button:has-text("Add Task")');
            
            await page.click('button:has-text("Tasks")');
            const checkbox = page.locator('input[type="checkbox"]').first();
            await checkbox.check();
            
            // Uncheck the task
            await checkbox.uncheck();
            
            // Verify task is no longer completed
            await expect(checkbox).not.toBeChecked();
            const taskItem = page.locator('.task-item').first();
            await expect(taskItem).not.toHaveClass(/completed/);
        });
    });

    test.describe('Task List Management', () => {
        test('should display multiple tasks correctly', async ({ page }) => {
            // Create multiple tasks
            const tasks = [
                { title: 'First Task', category: 'work', priority: 'high' },
                { title: 'Second Task', category: 'personal', priority: 'medium' },
                { title: 'Third Task', category: 'health', priority: 'low' }
            ];
            
            for (const task of tasks) {
                await page.click('button:has-text("Add Task")');
                await page.fill('input[placeholder*="task title"]', task.title);
                await page.selectOption('select[name="category"]', task.category);
                await page.selectOption('select[name="priority"]', task.priority);
                await page.click('button:has-text("Add Task")');
            }
            
            // Open task list
            await page.click('button:has-text("Tasks")');
            
            // Verify all tasks are displayed
            for (const task of tasks) {
                await expect(page.locator(`text=${task.title}`)).toBeVisible();
                await expect(page.locator(`text=${task.category}`)).toBeVisible();
                await expect(page.locator(`text=${task.priority}`)).toBeVisible();
            }
            
            // Verify task count
            await expect(page.locator('text=3 tasks')).toBeVisible();
        });

        test('should filter tasks by completion status', async ({ page }) => {
            // Create and complete some tasks
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Completed Task');
            await page.click('button:has-text("Add Task")');
            
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Pending Task');
            await page.click('button:has-text("Add Task")');
            
            // Complete first task
            await page.click('button:has-text("Tasks")');
            const firstCheckbox = page.locator('input[type="checkbox"]').first();
            await firstCheckbox.check();
            
            // Filter to show only pending tasks
            await page.click('button:has-text("Pending")');
            
            // Verify only pending task is visible
            await expect(page.locator('text=Pending Task')).toBeVisible();
            await expect(page.locator('text=Completed Task')).toBeHidden();
            
            // Filter to show completed tasks
            await page.click('button:has-text("Completed")');
            
            // Verify only completed task is visible
            await expect(page.locator('text=Completed Task')).toBeVisible();
            await expect(page.locator('text=Pending Task')).toBeHidden();
        });

        test('should delete tasks', async ({ page }) => {
            // Create a task
            await page.click('button:has-text("Add Task")');
            await page.fill('input[placeholder*="task title"]', 'Deletable Task');
            await page.click('button:has-text("Add Task")');
            
            // Open task list
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('text=Deletable Task')).toBeVisible();
            
            // Mock the confirm dialog to return true
            page.on('dialog', dialog => dialog.accept());
            
            // Delete the task
            const deleteButton = page.locator('button:has-text("Delete")').first();
            await deleteButton.click();
            
            // Verify task is removed
            await expect(page.locator('text=Deletable Task')).toBeHidden();
            await expect(page.locator('text=0 tasks')).toBeVisible();
        });
    });

    test.describe('UI State Management', () => {
        test('should maintain overlay states correctly', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            await expect(page.locator('button:has-text("Add Task")').first()).toHaveClass(/active/);
            
            // Switch to task list
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('.task-form-overlay')).toBeHidden();
            await expect(page.locator('.task-list-overlay')).toBeVisible();
            await expect(page.locator('button:has-text("Tasks")').first()).toHaveClass(/active/);
            await expect(page.locator('button:has-text("Add Task")').first()).not.toHaveClass(/active/);
            
            // Close overlay
            await page.click('.close-btn');
            await expect(page.locator('.task-list-overlay')).toBeHidden();
            await expect(page.locator('button:has-text("Tasks")').first()).not.toHaveClass(/active/);
        });

        test('should close overlays with escape key', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            
            // Press escape
            await page.keyboard.press('Escape');
            await expect(page.locator('.task-form-overlay')).toBeHidden();
            
            // Open task list
            await page.click('button:has-text("Tasks")');
            await expect(page.locator('.task-list-overlay')).toBeVisible();
            
            // Press escape
            await page.keyboard.press('Escape');
            await expect(page.locator('.task-list-overlay')).toBeHidden();
        });

        test('should close overlays when clicking background', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            await expect(page.locator('.task-form-overlay')).toBeVisible();
            
            // Click overlay background
            await page.click('.overlay', { position: { x: 10, y: 10 } });
            await expect(page.locator('.task-form-overlay')).toBeHidden();
        });
    });

    test.describe('Accessibility', () => {
        test('should support keyboard navigation', async ({ page }) => {
            // Open task form
            await page.click('button:has-text("Add Task")');
            
            // Tab through form elements
            await page.keyboard.press('Tab'); // Title input
            await expect(page.locator('input[placeholder*="task title"]')).toBeFocused();
            
            await page.keyboard.press('Tab'); // Description textarea
            await expect(page.locator('textarea[placeholder*="description"]')).toBeFocused();
            
            await page.keyboard.press('Tab'); // Category select
            await expect(page.locator('select[name="category"]')).toBeFocused();
            
            await page.keyboard.press('Tab'); // Priority select
            await expect(page.locator('select[name="priority"]')).toBeFocused();
            
            await page.keyboard.press('Tab'); // Submit button
            await expect(page.locator('button:has-text("Add Task")').last()).toBeFocused();
        });

        test('should have proper ARIA labels and roles', async ({ page }) => {
            await page.click('button:has-text("Add Task")');
            
            // Check form has proper role
            await expect(page.locator('form')).toHaveAttribute('role', 'form');
            
            // Check overlay has dialog role
            await expect(page.locator('.task-form-overlay')).toHaveAttribute('role', 'dialog');
            
            // Check inputs have proper labels
            await expect(page.locator('input[placeholder*="task title"]')).toHaveAttribute('aria-label');
            await expect(page.locator('textarea[placeholder*="description"]')).toHaveAttribute('aria-label');
        });
    });
});
