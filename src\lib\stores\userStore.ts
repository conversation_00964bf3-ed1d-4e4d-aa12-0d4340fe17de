import { writable, derived } from 'svelte/store';
import type { User } from '../types';

// Default user state
const defaultUser: User = {
  id: crypto.randomUUID(),
  bananaCount: 0,
  totalTasksCompleted: 0,
  totalGoalsCompleted: 0,
  unlockedFeatures: ['basic-tasks'], // Start with basic task functionality
  isPremium: false,
  createdAt: new Date(),
  lastActiveAt: new Date()
};

function createUserStore() {
  const { subscribe, set, update } = writable<User>(defaultUser);

  return {
    subscribe,
    set,
    addBananas: (amount: number) => {
      update(user => ({
        ...user,
        bananaCount: user.bananaCount + amount,
        lastActiveAt: new Date()
      }));
    },
    spendBananas: (amount: number) => {
      update(user => ({
        ...user,
        bananaCount: Math.max(0, user.bananaCount - amount),
        lastActiveAt: new Date()
      }));
    },
    incrementTasksCompleted: () => {
      update(user => ({
        ...user,
        totalTasksCompleted: user.totalTasksCompleted + 1,
        lastActiveAt: new Date()
      }));
    },
    incrementGoalsCompleted: () => {
      update(user => ({
        ...user,
        totalGoalsCompleted: user.totalGoalsCompleted + 1,
        lastActiveAt: new Date()
      }));
    },
    unlockFeature: (feature: string) => {
      update(user => ({
        ...user,
        unlockedFeatures: [...user.unlockedFeatures, feature],
        lastActiveAt: new Date()
      }));
    },
    setPremium: (isPremium: boolean) => {
      update(user => ({
        ...user,
        isPremium,
        lastActiveAt: new Date()
      }));
    },
    updateLastActive: () => {
      update(user => ({
        ...user,
        lastActiveAt: new Date()
      }));
    }
  };
}

export const userStore = createUserStore();

// Feature unlock costs (in bananas)
export const FEATURE_COSTS = {
  'categories': 100,
  'due-dates': 200,
  'priority-tags': 300,
  'habit-tracking': 500,
  'analytics': 750,
  'export-options': 1000,
  'collaboration': 1500,
  'calendar-sync': 2000,
  'custom-themes': 2500
} as const;

// Derived store to check if user can afford features
export const canAffordFeature = derived(
  userStore,
  $user => (feature: keyof typeof FEATURE_COSTS) => {
    const cost = FEATURE_COSTS[feature];
    return $user.bananaCount >= cost;
  }
);

// Derived store to check if feature is unlocked
export const hasFeature = derived(
  userStore,
  $user => (feature: string) => {
    return $user.isPremium || $user.unlockedFeatures.includes(feature);
  }
);
