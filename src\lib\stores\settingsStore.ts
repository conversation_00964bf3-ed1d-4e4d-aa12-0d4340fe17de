import { writable } from 'svelte/store';
import type { AppSettings } from '../types';

// Default settings
const defaultSettings: AppSettings = {
  soundEnabled: true,
  musicEnabled: true,
  retroFilter: false,
  highContrast: false,
  animationsEnabled: true
};

function createSettingsStore() {
  const { subscribe, set, update } = writable<AppSettings>(defaultSettings);

  return {
    subscribe,
    set,
    toggleSound: () => {
      update(settings => ({
        ...settings,
        soundEnabled: !settings.soundEnabled
      }));
    },
    toggleMusic: () => {
      update(settings => ({
        ...settings,
        musicEnabled: !settings.musicEnabled
      }));
    },
    toggleRetroFilter: () => {
      update(settings => ({
        ...settings,
        retroFilter: !settings.retroFilter
      }));
    },
    toggleHighContrast: () => {
      update(settings => ({
        ...settings,
        highContrast: !settings.highContrast
      }));
    },
    toggleAnimations: () => {
      update(settings => ({
        ...settings,
        animationsEnabled: !settings.animationsEnabled
      }));
    },
    updateSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => {
      update(settings => ({
        ...settings,
        [key]: value
      }));
    }
  };
}

export const settingsStore = createSettingsStore();
