// Mad Libs generator for the "Act Busy" feature

interface MadLibTemplate {
  template: string;
  bananaReward: number;
}

// Word lists for Mad Libs generation
const VERBS = [
  'Write', 'Review', 'Organize', 'Plan', 'Research', 'Update', 'Create', 'Design',
  'Analyze', 'Prepare', 'Schedule', 'Complete', 'Draft', 'Finalize', 'Submit',
  'Coordinate', 'Implement', 'Optimize', 'Evaluate', 'Document'
];

const NOUNS = [
  'report', 'presentation', 'proposal', 'document', 'spreadsheet', 'email',
  'meeting notes', 'project plan', 'budget', 'timeline', 'checklist', 'summary',
  'analysis', 'review', 'outline', 'draft', 'template', 'workflow', 'process',
  'strategy', 'agenda', 'memo', 'brief', 'specification', 'requirements'
];

const TIME_PHRASES = [
  'by Friday', 'by end of day', 'by tomorrow', 'by next week', 'by Monday',
  'before the meeting', 'by noon', 'this afternoon', 'by 5 PM', 'before lunch',
  'by the deadline', 'ASAP', 'by Thursday', 'before the weekend', 'by next month'
];

const ADJECTIVES = [
  'important', 'urgent', 'detailed', 'comprehensive', 'thorough', 'quick',
  'preliminary', 'final', 'updated', 'revised', 'new', 'improved', 'strategic',
  'critical', 'essential', 'priority', 'high-level', 'in-depth', 'focused'
];

const LOCATIONS = [
  'for the client', 'for the team', 'for management', 'for the project',
  'for the department', 'for the meeting', 'for review', 'for approval',
  'for the presentation', 'for the stakeholders', 'for the board',
  'for the committee', 'for the workshop', 'for the conference'
];

// Mad Lib templates
const MAD_LIB_TEMPLATES: MadLibTemplate[] = [
  {
    template: '[VERB] the [ADJECTIVE] [NOUN] [TIME_PHRASE]',
    bananaReward: 5
  },
  {
    template: '[VERB] [ADJECTIVE] [NOUN] [LOCATION] [TIME_PHRASE]',
    bananaReward: 7
  },
  {
    template: '[VERB] and [VERB] the [NOUN] [TIME_PHRASE]',
    bananaReward: 6
  },
  {
    template: '[VERB] [ADJECTIVE] [NOUN] and send [LOCATION] [TIME_PHRASE]',
    bananaReward: 8
  },
  {
    template: '[VERB] the [NOUN] [LOCATION] and [VERB] [TIME_PHRASE]',
    bananaReward: 7
  }
];

/**
 * Get a random element from an array
 */
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate a random Mad Libs task
 */
export function generateMadLibTask(): { title: string; bananaReward: number } {
  const template = getRandomElement(MAD_LIB_TEMPLATES);
  
  let title = template.template;
  
  // Replace placeholders with random words
  title = title.replace(/\[VERB\]/g, () => getRandomElement(VERBS));
  title = title.replace(/\[NOUN\]/g, () => getRandomElement(NOUNS));
  title = title.replace(/\[TIME_PHRASE\]/g, () => getRandomElement(TIME_PHRASES));
  title = title.replace(/\[ADJECTIVE\]/g, () => getRandomElement(ADJECTIVES));
  title = title.replace(/\[LOCATION\]/g, () => getRandomElement(LOCATIONS));
  
  return {
    title,
    bananaReward: template.bananaReward
  };
}

/**
 * Generate multiple Mad Libs tasks
 */
export function generateMultipleMadLibTasks(count: number): Array<{ title: string; bananaReward: number }> {
  const tasks = [];
  for (let i = 0; i < count; i++) {
    tasks.push(generateMadLibTask());
  }
  return tasks;
}

/**
 * Check if user has reached daily Act Busy limit (for free users)
 */
export function hasReachedActBusyLimit(usedToday: number, isPremium: boolean): boolean {
  if (isPremium) return false;
  return usedToday >= 5; // Free users get 5 uses per day
}

/**
 * Get remaining Act Busy uses for today
 */
export function getRemainingActBusyUses(usedToday: number, isPremium: boolean): number {
  if (isPremium) return Infinity;
  return Math.max(0, 5 - usedToday);
}
