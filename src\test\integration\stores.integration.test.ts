import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { taskStore, userStore, gameStore, inputStore } from '$lib/stores';
import { completedTasks, pendingTasks, tasksByCategory } from '$lib/stores/taskStore';
import { canAffordFeature, FEATURE_COSTS } from '$lib/stores/userStore';
import { GAME_UPGRADES } from '$lib/stores/gameStore';

describe('Store Integration Tests', () => {
    beforeEach(() => {
        // Reset all stores before each test
        taskStore.clear();
        userStore.set({
            id: 'test-user',
            bananaCount: 1000,
            unlockedFeatures: [],
            isPremium: false,
            createdAt: new Date(),
            lastActiveAt: new Date()
        });
        gameStore.set({
            monkeyPosition: { x: 100, y: 500 },
            currentScene: 'jungle',
            unlockedAreas: ['starting-grove'],
            cosmetics: {
                monkeySkin: 'default',
                theme: 'jungle'
            },
            upgrades: []
        });
        inputStore.set({
            left: false,
            right: false,
            up: false,
            down: false,
            jump: false,
            interact: false
        });
    });

    describe('TaskStore and UserStore Integration', () => {
        it('should award bananas when task is completed', () => {
            const initialBananas = get(userStore).bananaCount;

            // Add a task
            const task = taskStore.add({
                title: 'Test Task',
                description: 'Test Description',
                category: 'personal',
                priority: 'medium',
                completed: false
            });

            // Complete the task
            taskStore.complete(task.id);

            // Check that bananas were awarded
            const finalBananas = get(userStore).bananaCount;
            expect(finalBananas).toBeGreaterThan(initialBananas);
        });

        it('should track task completion statistics', () => {
            // Add multiple tasks
            const task1 = taskStore.add({ title: 'Task 1', description: '', category: 'work', priority: 'high', completed: false });
            taskStore.add({ title: 'Task 2', description: '', category: 'personal', priority: 'medium', completed: false });
            const task3 = taskStore.add({ title: 'Task 3', description: '', category: 'work', priority: 'low', completed: false });

            // Complete some tasks
            taskStore.complete(task1.id);
            taskStore.complete(task3.id);

            // Check derived stores
            const completed = get(completedTasks);
            const pending = get(pendingTasks);

            expect(completed).toHaveLength(2);
            expect(pending).toHaveLength(1);
            expect(completed.map(t => t.title)).toEqual(['Task 1', 'Task 3']);
            expect(pending[0].title).toBe('Task 2');
        });

        it('should organize tasks by category', () => {
            // Add tasks in different categories
            taskStore.add({ title: 'Work Task 1', description: '', category: 'work', priority: 'high', completed: false });
            taskStore.add({ title: 'Personal Task 1', description: '', category: 'personal', priority: 'medium', completed: false });
            taskStore.add({ title: 'Work Task 2', description: '', category: 'work', priority: 'low', completed: false });
            taskStore.add({ title: 'Health Task 1', description: '', category: 'health', priority: 'high', completed: false });

            const categorized = get(tasksByCategory);

            expect(categorized.work).toHaveLength(2);
            expect(categorized.personal).toHaveLength(1);
            expect(categorized.health).toHaveLength(1);
            expect(categorized.work.map(t => t.title)).toEqual(['Work Task 1', 'Work Task 2']);
        });

        it('should handle feature unlocking with banana costs', () => {
            // Start with enough bananas
            userStore.addBananas(500);

            const canAfford = get(canAffordFeature);
            expect(canAfford('categories')).toBe(true); // costs 100
            expect(canAfford('due-dates')).toBe(true); // costs 200
            expect(canAfford('custom-themes')).toBe(false); // costs 2500

            // Unlock a feature
            userStore.unlockFeature('categories');

            const user = get(userStore);
            expect(user.unlockedFeatures).toContain('categories');
            expect(user.bananaCount).toBe(1500 - FEATURE_COSTS.categories); // 1500 - 100 = 1400
        });
    });

    describe('GameStore and UserStore Integration', () => {
        it('should track monkey position changes', () => {
            const initialPosition = get(gameStore).monkeyPosition;
            expect(initialPosition).toEqual({ x: 100, y: 500 });

            gameStore.updateMonkeyPosition(200, 300);

            const newPosition = get(gameStore).monkeyPosition;
            expect(newPosition).toEqual({ x: 200, y: 300 });
        });

        it('should handle scene transitions', () => {
            expect(get(gameStore).currentScene).toBe('jungle');

            gameStore.changeScene('task-grove');

            expect(get(gameStore).currentScene).toBe('task-grove');
        });

        it('should unlock new areas', () => {
            const initialAreas = get(gameStore).unlockedAreas;
            expect(initialAreas).toEqual(['starting-grove']);

            gameStore.unlockArea('banana-forest');

            const newAreas = get(gameStore).unlockedAreas;
            expect(newAreas).toContain('starting-grove');
            expect(newAreas).toContain('banana-forest');
        });

        it('should handle game upgrades with banana costs', () => {
            // Set enough bananas for upgrades
            userStore.addBananas(1000);

            const upgrade = GAME_UPGRADES.find(u => u.id === 'faster-monkey');
            expect(upgrade).toBeDefined();

            // Purchase upgrade
            gameStore.purchaseUpgrade(upgrade!);
            userStore.spendBananas(upgrade!.cost);

            const gameState = get(gameStore);
            const userState = get(userStore);

            expect(gameState.upgrades).toHaveLength(1);
            expect(gameState.upgrades[0].id).toBe('faster-monkey');
            expect(gameState.upgrades[0].purchased).toBe(true);
            expect(userState.bananaCount).toBe(2000 - upgrade!.cost);
        });

        it('should update cosmetics', () => {
            const initialCosmetics = get(gameStore).cosmetics;
            expect(initialCosmetics.monkeySkin).toBe('default');
            expect(initialCosmetics.theme).toBe('jungle');

            gameStore.updateCosmetics({ monkeySkin: 'golden', theme: 'desert' });

            const newCosmetics = get(gameStore).cosmetics;
            expect(newCosmetics.monkeySkin).toBe('golden');
            expect(newCosmetics.theme).toBe('desert');
        });
    });

    describe('InputStore Integration', () => {
        it('should track input state changes', () => {
            const initialInput = get(inputStore);
            expect(initialInput.left).toBe(false);
            expect(initialInput.jump).toBe(false);

            inputStore.setInput('left', true);
            inputStore.setInput('jump', true);

            const newInput = get(inputStore);
            expect(newInput.left).toBe(true);
            expect(newInput.jump).toBe(true);
            expect(newInput.right).toBe(false); // unchanged
        });

        it('should handle multiple input changes', () => {
            inputStore.setInput('left', true);
            inputStore.setInput('right', true);
            inputStore.setInput('jump', true);

            const input = get(inputStore);
            expect(input.left).toBe(true);
            expect(input.right).toBe(true);
            expect(input.jump).toBe(true);
            expect(input.up).toBe(false);
            expect(input.down).toBe(false);
            expect(input.interact).toBe(false);
        });

        it('should reset all inputs', () => {
            // Set some inputs
            inputStore.setInput('left', true);
            inputStore.setInput('jump', true);
            inputStore.setInput('interact', true);

            // Reset all
            inputStore.resetAll();

            const input = get(inputStore);
            expect(input.left).toBe(false);
            expect(input.right).toBe(false);
            expect(input.up).toBe(false);
            expect(input.down).toBe(false);
            expect(input.jump).toBe(false);
            expect(input.interact).toBe(false);
        });
    });

    describe('Cross-Store Data Flow', () => {
        it('should handle complete task workflow with banana rewards', () => {
            const initialBananas = get(userStore).bananaCount;

            // Add task
            const task = taskStore.add({
                title: 'Workflow Task',
                description: 'Complete workflow test',
                category: 'work',
                priority: 'high',
                completed: false
            });

            // Complete task (this should trigger banana reward)
            taskStore.complete(task.id);

            // Verify task completion
            const completedTasksList = get(completedTasks);
            expect(completedTasksList).toHaveLength(1);
            expect(completedTasksList[0].id).toBe(task.id);
            expect(completedTasksList[0].completed).toBe(true);
            expect(completedTasksList[0].completedAt).toBeInstanceOf(Date);

            // Verify banana reward (this would be handled by component logic)
            // In a real app, completing a task would trigger userStore.addBananas()
            userStore.addBananas(10); // Simulate the reward

            const finalBananas = get(userStore).bananaCount;
            expect(finalBananas).toBe(initialBananas + 10);
        });

        it('should handle feature unlock workflow', () => {
            // Start with enough bananas
            userStore.addBananas(500);

            // Check if user can afford feature
            const canAfford = get(canAffordFeature);
            expect(canAfford('categories')).toBe(true);

            // Unlock feature
            userStore.unlockFeature('categories');

            // Verify feature is unlocked and bananas are spent
            const user = get(userStore);
            expect(user.unlockedFeatures).toContain('categories');
            expect(user.bananaCount).toBe(1500 - FEATURE_COSTS.categories);

            // Verify user can no longer afford the same feature (already unlocked)
            expect(user.unlockedFeatures.includes('categories')).toBe(true);
        });

        it('should handle game progression workflow', () => {
            // Start game progression
            gameStore.updateMonkeyPosition(150, 400);
            gameStore.unlockArea('banana-forest');

            // Complete tasks to earn bananas
            const task1 = taskStore.add({ title: 'Game Task 1', description: '', category: 'personal', priority: 'medium', completed: false });
            const task2 = taskStore.add({ title: 'Game Task 2', description: '', category: 'personal', priority: 'medium', completed: false });

            taskStore.complete(task1.id);
            taskStore.complete(task2.id);

            // Simulate banana rewards
            userStore.addBananas(20); // 10 per task

            // Purchase game upgrade
            const upgrade = GAME_UPGRADES.find(u => u.id === 'faster-monkey');
            gameStore.purchaseUpgrade(upgrade!);
            userStore.spendBananas(upgrade!.cost);

            // Verify final state
            const gameState = get(gameStore);
            const userState = get(userStore);
            const completedTasksList = get(completedTasks);

            expect(gameState.monkeyPosition).toEqual({ x: 150, y: 400 });
            expect(gameState.unlockedAreas).toContain('banana-forest');
            expect(gameState.upgrades).toHaveLength(1);
            expect(gameState.upgrades[0].id).toBe('faster-monkey');
            expect(completedTasksList).toHaveLength(2);
            expect(userState.bananaCount).toBe(1000 + 20 - upgrade!.cost);
        });
    });

    describe('Store Persistence and State Management', () => {
        it('should maintain referential integrity across store updates', () => {
            // Add initial data
            const task = taskStore.add({ title: 'Ref Task', description: '', category: 'work', priority: 'medium', completed: false });
            userStore.addBananas(100);
            gameStore.updateMonkeyPosition(200, 300);

            // Get initial references
            const initialUser = get(userStore);
            const initialGame = get(gameStore);

            // Make updates
            taskStore.update(task.id, { title: 'Updated Ref Task' });
            userStore.addBananas(50);
            gameStore.updateMonkeyPosition(250, 350);

            // Get updated references
            const updatedTasks = get(taskStore);
            const updatedUser = get(userStore);
            const updatedGame = get(gameStore);

            // Verify updates
            expect(updatedTasks[0].title).toBe('Updated Ref Task');
            expect(updatedUser.bananaCount).toBe(initialUser.bananaCount + 50);
            expect(updatedGame.monkeyPosition).toEqual({ x: 250, y: 350 });

            // Verify other data is preserved
            expect(updatedTasks[0].id).toBe(task.id);
            expect(updatedUser.id).toBe(initialUser.id);
            expect(updatedGame.currentScene).toBe(initialGame.currentScene);
        });

        it('should handle concurrent store updates', () => {
            // Simulate concurrent updates that might happen in the UI
            const task1 = taskStore.add({ title: 'Concurrent 1', description: '', category: 'work', priority: 'high', completed: false });
            const task2 = taskStore.add({ title: 'Concurrent 2', description: '', category: 'personal', priority: 'medium', completed: false });

            // Perform multiple operations
            taskStore.complete(task1.id);
            userStore.addBananas(10);
            gameStore.updateMonkeyPosition(300, 400);
            taskStore.complete(task2.id);
            userStore.addBananas(10);
            gameStore.unlockArea('new-area');

            // Verify final state
            const tasks = get(taskStore);
            const user = get(userStore);
            const game = get(gameStore);

            expect(tasks.filter(t => t.completed)).toHaveLength(2);
            expect(user.bananaCount).toBe(1020); // 1000 + 10 + 10
            expect(game.monkeyPosition).toEqual({ x: 300, y: 400 });
            expect(game.unlockedAreas).toContain('new-area');
        });
    });
});
