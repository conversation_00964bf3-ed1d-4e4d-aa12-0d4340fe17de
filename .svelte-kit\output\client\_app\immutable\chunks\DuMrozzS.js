import{B as v}from"./Cv0AAtAs.js";import{T as h,D as i,n as u}from"./BgykbHj9.js";const p={};function P(n,t,o){let e=2166136261;for(let a=0;a<t;a++)e^=n[a].uid,e=Math.imul(e,16777619),e>>>=0;return p[e]||x(n,t,e,o)}function x(n,t,o,e){const a={};let s=0;for(let c=0;c<e;c++){const l=c<t?n[c]:h.EMPTY.source;a[s++]=l.source,a[s++]=l.style}const r=new v(a);return p[o]=r,r}class C{constructor(t){this._canvasPool=Object.create(null),this.canvasOptions=t||{},this.enableFullScreen=!1}_createCanvasAndContext(t,o){const e=i.get().createCanvas();e.width=t,e.height=o;const a=e.getContext("2d");return{canvas:e,context:a}}getOptimalCanvasAndContext(t,o,e=1){t=Math.ceil(t*e-1e-6),o=Math.ceil(o*e-1e-6),t=u(t),o=u(o);const a=(t<<17)+(o<<1);this._canvasPool[a]||(this._canvasPool[a]=[]);let s=this._canvasPool[a].pop();return s||(s=this._createCanvasAndContext(t,o)),s}returnCanvasAndContext(t){const o=t.canvas,{width:e,height:a}=o,s=(e<<17)+(a<<1);t.context.resetTransform(),t.context.clearRect(0,0,e,a),this._canvasPool[s].push(t)}clear(){this._canvasPool={}}}const _=new C;export{_ as C,P as g};
