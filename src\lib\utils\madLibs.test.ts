import { describe, it, expect } from 'vitest';
import {
    generateMadLibTask,
    generateMultipleMadLibTasks,
    hasReachedActBusyLimit,
    getRemainingActBusyUses
} from './madLibs';

describe('MadLibs', () => {

    describe('generateMadLibTask', () => {
        it('should generate a task with title and bananaReward', () => {
            const task = generateMadLibTask();

            expect(task).toHaveProperty('title');
            expect(task).toHaveProperty('bananaReward');
            expect(typeof task.title).toBe('string');
            expect(typeof task.bananaReward).toBe('number');
            expect(task.title.length).toBeGreaterThan(0);
            expect(task.bananaReward).toBeGreaterThan(0);
        });

        it('should generate different tasks on multiple calls', () => {
            const task1 = generateMadLibTask();
            const task2 = generateMadLibTask();
            const task3 = generateMadLibTask();

            // At least one should be different (very high probability)
            const allSame = task1.title === task2.title &&
                task2.title === task3.title &&
                task1.bananaReward === task2.bananaReward &&
                task2.bananaReward === task3.bananaReward;

            expect(allSame).toBe(false);
        });

        it('should not contain placeholder brackets in generated text', () => {
            const task = generateMadLibTask();

            expect(task.title).not.toMatch(/\[[^\]]*\]/);
        });

        it('should generate tasks with reasonable length', () => {
            const task = generateMadLibTask();

            expect(task.title.length).toBeLessThan(100);
            expect(task.title.length).toBeGreaterThan(5);
        });
    });

    describe('generateMultipleMadLibTasks', () => {
        it('should generate the requested number of tasks', () => {
            const tasks = generateMultipleMadLibTasks(3);
            expect(tasks).toHaveLength(3);

            tasks.forEach(task => {
                expect(task).toHaveProperty('title');
                expect(task).toHaveProperty('bananaReward');
            });
        });

        it('should generate different tasks', () => {
            const tasks = generateMultipleMadLibTasks(5);
            const titles = tasks.map(task => task.title);
            const uniqueTitles = new Set(titles);

            // Should have some variety (not all identical)
            expect(uniqueTitles.size).toBeGreaterThan(1);
        });
    });

    describe('hasReachedActBusyLimit', () => {
        it('should return false for premium users', () => {
            const hasReached = hasReachedActBusyLimit(10, true);
            expect(hasReached).toBe(false);
        });

        it('should respect daily limits for free users', () => {
            expect(hasReachedActBusyLimit(2, false)).toBe(false);
            expect(hasReachedActBusyLimit(5, false)).toBe(true);
            expect(hasReachedActBusyLimit(10, false)).toBe(true);
        });
    });

    describe('getRemainingActBusyUses', () => {
        it('should return correct remaining uses for free users', () => {
            const remaining = getRemainingActBusyUses(2, false);
            expect(remaining).toBe(3); // 5 - 2 = 3
        });

        it('should return unlimited for premium users', () => {
            const remaining = getRemainingActBusyUses(100, true);
            expect(remaining).toBe(Infinity);
        });

        it('should not return negative values', () => {
            const remaining = getRemainingActBusyUses(10, false);
            expect(remaining).toBe(0);
        });
    });
});
