import { describe, it, expect, beforeEach } from 'vitest';
import { BananaCalculator } from './bananaCalculator';
import type { Task } from '$lib/types';

describe('BananaCalculator', () => {
    let calculator: BananaCalculator;

    beforeEach(() => {
        // Reset singleton instance for each test
        (BananaCalculator as any).instance = null;
        calculator = BananaCalculator.getInstance();
    });

    describe('getInstance', () => {
        it('should return the same instance (singleton pattern)', () => {
            const instance1 = BananaCalculator.getInstance();
            const instance2 = BananaCalculator.getInstance();
            expect(instance1).toBe(instance2);
        });
    });

    describe('calculateTaskReward', () => {
        it('should calculate basic reward for simple task', () => {
            const task: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Simple task',
                description: 'A basic task',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(task);
            expect(reward).toBe(12); // 10 (medium) + 2 (description)
            expect(typeof reward).toBe('number');
        });

        it('should give higher rewards for high priority tasks', () => {
            const lowPriorityTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Low priority task',
                priority: 'low',
                completed: false,
                bananaReward: 0
            };

            const highPriorityTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'High priority task',
                priority: 'high',
                completed: false,
                bananaReward: 0
            };

            const lowReward = calculator.calculateTaskReward(lowPriorityTask);
            const highReward = calculator.calculateTaskReward(highPriorityTask);

            expect(highReward).toBeGreaterThan(lowReward);
            expect(lowReward).toBe(5); // low priority base
            expect(highReward).toBe(15); // high priority base
        });

        it('should give higher rewards for tasks with due dates', () => {
            const taskWithoutDueDate: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task without due date',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const taskWithDueDate: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task with due date',
                priority: 'medium',
                dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // tomorrow
                completed: false,
                bananaReward: 0
            };

            const rewardWithoutDueDate = calculator.calculateTaskReward(taskWithoutDueDate);
            const rewardWithDueDate = calculator.calculateTaskReward(taskWithDueDate);

            expect(rewardWithDueDate).toBeGreaterThan(rewardWithoutDueDate);
            expect(rewardWithoutDueDate).toBe(10); // medium priority only
            expect(rewardWithDueDate).toBe(11); // medium priority + due date bonus
        });

        it('should give bonus for descriptions', () => {
            const taskWithoutDesc: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const taskWithDesc: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task',
                description: 'This has a description',
                priority: 'medium',
                completed: false,
                bananaReward: 0
            };

            const rewardWithoutDesc = calculator.calculateTaskReward(taskWithoutDesc);
            const rewardWithDesc = calculator.calculateTaskReward(taskWithDesc);

            expect(rewardWithDesc).toBeGreaterThan(rewardWithoutDesc);
            expect(rewardWithoutDesc).toBe(10); // medium priority only
            expect(rewardWithDesc).toBe(12); // medium priority + description bonus
        });

        it('should handle tasks with categories', () => {
            const taskWithCategory: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Categorized task',
                priority: 'medium',
                category: 'Work',
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(taskWithCategory);
            expect(reward).toBe(11); // medium priority + category bonus
        });

        it('should not give category bonus for empty category', () => {
            const taskWithEmptyCategory: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task with empty category',
                priority: 'medium',
                category: '   ', // whitespace only
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(taskWithEmptyCategory);
            expect(reward).toBe(10); // medium priority only, no category bonus
        });

        it('should not give description bonus for empty description', () => {
            const taskWithEmptyDesc: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Task with empty description',
                priority: 'medium',
                description: '   ', // whitespace only
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(taskWithEmptyDesc);
            expect(reward).toBe(10); // medium priority only, no description bonus
        });

        it('should calculate maximum possible reward', () => {
            const maxTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Maximum reward task',
                description: 'This task has everything',
                priority: 'high',
                category: 'Important',
                dueDate: new Date(),
                completed: false,
                bananaReward: 0
            };

            const reward = calculator.calculateTaskReward(maxTask);
            expect(reward).toBe(19); // 15 (high) + 2 (description) + 1 (due date) + 1 (category)
        });

        it('should return 0 for task without priority', () => {
            const emptyTask: Omit<Task, 'id' | 'createdAt'> = {
                title: 'Empty task',
                priority: 'low', // We need to provide a priority since it's required
                completed: false,
                bananaReward: 0
            };
            const reward = calculator.calculateTaskReward(emptyTask);
            expect(reward).toBe(5); // Low priority base reward
        });
    });

    describe('calculateFeatureUnlockCost', () => {
        it('should return correct cost for known features', () => {
            const categoriesCost = calculator.calculateFeatureUnlockCost('categories');
            expect(categoriesCost).toBe(100);

            const customThemesCost = calculator.calculateFeatureUnlockCost('custom-themes');
            expect(customThemesCost).toBe(2500);
        });

        it('should return default cost for unknown features', () => {
            const unknownCost = calculator.calculateFeatureUnlockCost('unknownFeature' as any);
            expect(unknownCost).toBe(100);
        });

        it('should scale cost with user level', () => {
            const level1Cost = calculator.calculateFeatureUnlockCost('categories', 1);
            const level5Cost = calculator.calculateFeatureUnlockCost('categories', 5);

            expect(level5Cost).toBeGreaterThan(level1Cost);
        });
    });

    describe('calculateUpgradeCost', () => {
        it('should return correct cost for known upgrades', () => {
            const fasterMonkeyCost = calculator.calculateUpgradeCost('faster-monkey', 0);
            expect(fasterMonkeyCost).toBe(250);

            const doubleJumpCost = calculator.calculateUpgradeCost('double-jump', 0);
            expect(doubleJumpCost).toBe(750);
        });

        it('should scale cost with level', () => {
            const level0Cost = calculator.calculateUpgradeCost('faster-monkey', 0);
            const level1Cost = calculator.calculateUpgradeCost('faster-monkey', 1);
            const level2Cost = calculator.calculateUpgradeCost('faster-monkey', 2);

            expect(level1Cost).toBeGreaterThan(level0Cost);
            expect(level2Cost).toBeGreaterThan(level1Cost);
        });

        it('should return default cost for unknown upgrades', () => {
            const unknownCost = calculator.calculateUpgradeCost('unknownUpgrade' as any, 0);
            expect(unknownCost).toBe(500); // Default base cost
        });

        it('should handle level 0', () => {
            const level0Cost = calculator.calculateUpgradeCost('faster-monkey', 0);
            expect(level0Cost).toBe(250); // Should return base cost
        });
    });

    describe('applyMultipliers', () => {
        it('should apply multipliers correctly', () => {
            const baseReward = 100;
            const upgrades: any[] = []; // Empty upgrades
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(100); // No multipliers applied
        });

        it('should apply streak bonus', () => {
            const baseReward = 100;
            const upgrades: any[] = [];
            const result = calculator.applyMultipliers(baseReward, upgrades, 10);
            expect(result).toBe(150); // 50% streak bonus for 10 days
        });

        it('should apply banana_boost_25 upgrade multiplier', () => {
            const baseReward = 100;
            const upgrades = [
                { id: 'boost1', effect: 'banana_boost_25', purchased: true }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(125); // 25% boost
        });

        it('should apply banana_boost_50 upgrade multiplier', () => {
            const baseReward = 100;
            const upgrades = [
                { id: 'boost2', effect: 'banana_boost_50', purchased: true }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(150); // 50% boost
        });

        it('should apply double_bananas upgrade multiplier', () => {
            const baseReward = 100;
            const upgrades = [
                { id: 'double', effect: 'double_bananas', purchased: true }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(200); // 2x multiplier
        });

        it('should ignore unpurchased upgrades', () => {
            const baseReward = 100;
            const upgrades = [
                { id: 'boost1', effect: 'banana_boost_25', purchased: false },
                { id: 'boost2', effect: 'banana_boost_50', purchased: false }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(100); // No multipliers applied
        });

        it('should combine multiple upgrade multipliers', () => {
            const baseReward = 100;
            const upgrades = [
                { id: 'boost1', effect: 'banana_boost_25', purchased: true },
                { id: 'boost2', effect: 'banana_boost_50', purchased: true }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(175); // 1 + 0.25 + 0.5 = 1.75x multiplier
        });

        it('should combine upgrades with streak bonus', () => {
            const baseReward = 100;
            const upgrades = [
                { id: 'boost1', effect: 'banana_boost_25', purchased: true }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 4); // 4 days = 20% streak bonus
            expect(result).toBe(145); // (1 + 0.25 + 0.2) * 100 = 145
        });

        it('should cap streak bonus at 50%', () => {
            const baseReward = 100;
            const upgrades: any[] = [];
            const result = calculator.applyMultipliers(baseReward, upgrades, 20); // 20 days would be 100% but capped at 50%
            expect(result).toBe(150); // 50% max streak bonus
        });

        it('should floor the final result', () => {
            const baseReward = 33;
            const upgrades = [
                { id: 'boost1', effect: 'banana_boost_25', purchased: true }
            ];
            const result = calculator.applyMultipliers(baseReward, upgrades, 0);
            expect(result).toBe(41); // Math.floor(33 * 1.25) = Math.floor(41.25) = 41
        });
    });

    describe('calculateQuestReward', () => {
        it('should calculate reward for daily quest', () => {
            const quest = {
                id: 'daily1',
                title: 'Daily Quest',
                description: 'Complete 5 tasks',
                type: 'daily' as const,
                bananaReward: 50,
                completed: false,
                progress: 0,
                target: 5
            };

            const reward = calculator.calculateQuestReward(quest);
            expect(reward).toBe(50); // No bonus for daily quests
        });

        it('should calculate reward for weekly quest with 50% bonus', () => {
            const quest = {
                id: 'weekly1',
                title: 'Weekly Quest',
                description: 'Complete 20 tasks this week',
                type: 'weekly' as const,
                bananaReward: 100,
                completed: false,
                progress: 0,
                target: 20
            };

            const reward = calculator.calculateQuestReward(quest);
            expect(reward).toBe(150); // 100 * 1.5 = 150
        });

        it('should calculate reward for achievement quest with 100% bonus', () => {
            const quest = {
                id: 'achievement1',
                title: 'Achievement Quest',
                description: 'Complete 100 tasks total',
                type: 'achievement' as const,
                bananaReward: 200,
                completed: false,
                progress: 0,
                target: 100
            };

            const reward = calculator.calculateQuestReward(quest);
            expect(reward).toBe(400); // 200 * 2 = 400
        });

        it('should floor the final reward', () => {
            const quest = {
                id: 'weekly2',
                title: 'Weekly Quest',
                description: 'Complete tasks',
                type: 'weekly' as const,
                bananaReward: 33, // 33 * 1.5 = 49.5, should floor to 49
                completed: false,
                progress: 0,
                target: 10
            };

            const reward = calculator.calculateQuestReward(quest);
            expect(reward).toBe(49); // Math.floor(33 * 1.5) = 49
        });
    });

    describe('calculatePassiveIncome', () => {
        it('should return 0 for no upgrades', () => {
            const upgrades: any[] = [];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(0);
        });

        it('should calculate income from auto_harvest_1 upgrade', () => {
            const upgrades = [
                { id: 'auto1', effect: 'auto_harvest_1', purchased: true }
            ];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(60); // 1 per minute = 60 per hour
        });

        it('should calculate income from auto_harvest_5 upgrade', () => {
            const upgrades = [
                { id: 'auto5', effect: 'auto_harvest_5', purchased: true }
            ];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(300); // 5 per minute = 300 per hour
        });

        it('should calculate income from banana_tree upgrade', () => {
            const upgrades = [
                { id: 'tree', effect: 'banana_tree', purchased: true }
            ];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(100); // Passive banana tree
        });

        it('should ignore unpurchased upgrades', () => {
            const upgrades = [
                { id: 'auto1', effect: 'auto_harvest_1', purchased: false },
                { id: 'tree', effect: 'banana_tree', purchased: false }
            ];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(0);
        });

        it('should combine multiple passive income sources', () => {
            const upgrades = [
                { id: 'auto1', effect: 'auto_harvest_1', purchased: true },
                { id: 'auto5', effect: 'auto_harvest_5', purchased: true },
                { id: 'tree', effect: 'banana_tree', purchased: true }
            ];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(460); // 60 + 300 + 100 = 460
        });

        it('should ignore unknown upgrade effects', () => {
            const upgrades = [
                { id: 'unknown', effect: 'unknown_effect', purchased: true },
                { id: 'tree', effect: 'banana_tree', purchased: true }
            ];
            const income = calculator.calculatePassiveIncome(upgrades);
            expect(income).toBe(100); // Only banana_tree counted
        });
    });

    describe('calculateNextMilestone', () => {
        it('should find next affordable milestone', () => {
            const milestone = calculator.calculateNextMilestone(50, []);
            expect(milestone).toEqual({
                feature: 'categories',
                cost: 100,
                remaining: 50
            });
        });

        it('should return null when all features are unlocked', () => {
            const allFeatures = ['categories', 'due-dates', 'priority-tags', 'habit-tracking',
                'analytics', 'export-options', 'collaboration', 'calendar-sync', 'custom-themes'];
            const milestone = calculator.calculateNextMilestone(10000, allFeatures);
            expect(milestone).toBeNull();
        });

        it('should skip already unlocked features', () => {
            const unlockedFeatures = ['categories', 'due-dates'];
            const milestone = calculator.calculateNextMilestone(250, unlockedFeatures);
            expect(milestone).toEqual({
                feature: 'priority-tags',
                cost: 300,
                remaining: 50
            });
        });

        it('should return null when user can afford all remaining features', () => {
            const unlockedFeatures = ['categories', 'due-dates', 'priority-tags', 'habit-tracking',
                'analytics', 'export-options', 'collaboration', 'calendar-sync'];
            const milestone = calculator.calculateNextMilestone(3000, unlockedFeatures); // Can afford custom-themes (2500)
            expect(milestone).toBeNull();
        });

        it('should find the next unaffordable feature correctly', () => {
            const unlockedFeatures = ['categories'];
            const milestone = calculator.calculateNextMilestone(150, unlockedFeatures); // Can afford due-dates (200) but not priority-tags (300)
            expect(milestone).toEqual({
                feature: 'due-dates',
                cost: 200,
                remaining: 50
            });
        });
    });
});
