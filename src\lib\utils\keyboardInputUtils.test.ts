import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock DOM elements for testing
function createMockElement(tagName: string, type?: string): HTMLElement {
    const element = {
        tagName: tagName.toUpperCase(),
        type: type || '',
        contentEditable: 'false',
        setAttribute: vi.fn(),
        getAttribute: vi.fn(),
        // Add instanceof check support
        constructor: {
            name: 'HTMLElement'
        }
    } as unknown as HTMLElement;

    // Make instanceof HTMLElement work
    Object.setPrototypeOf(element, HTMLElement.prototype);

    return element;
}

// Core keyboard input logic extracted from GameCanvas component
function isTypingInForm(target: EventTarget | null): boolean {
    if (!target || !(target instanceof HTMLElement)) {
        return false;
    }

    const element = target as HTMLElement;
    const tagName = element.tagName.toLowerCase();

    // Check for form input elements
    if (tagName === 'input' || tagName === 'textarea' || tagName === 'select') {
        return true;
    }

    // Check for contentEditable elements
    if (element.contentEditable === 'true') {
        return true;
    }

    return false;
}

describe('Keyboard Input Management Utils', () => {
    describe('isTypingInForm', () => {
        it('should return true for input elements', () => {
            const input = createMockElement('input', 'text');
            expect(isTypingInForm(input)).toBe(true);
        });

        it('should return true for textarea elements', () => {
            const textarea = createMockElement('textarea');
            expect(isTypingInForm(textarea)).toBe(true);
        });

        it('should return true for select elements', () => {
            const select = createMockElement('select');
            expect(isTypingInForm(select)).toBe(true);
        });

        it('should return true for contentEditable elements', () => {
            const div = createMockElement('div');
            div.contentEditable = 'true';
            expect(isTypingInForm(div)).toBe(true);
        });

        it('should return false for regular div elements', () => {
            const div = createMockElement('div');
            expect(isTypingInForm(div)).toBe(false);
        });

        it('should return false for button elements', () => {
            const button = createMockElement('button');
            expect(isTypingInForm(button)).toBe(false);
        });

        it('should return false for canvas elements', () => {
            const canvas = createMockElement('canvas');
            expect(isTypingInForm(canvas)).toBe(false);
        });

        it('should return false for null target', () => {
            expect(isTypingInForm(null)).toBe(false);
        });

        it('should return false for non-HTMLElement targets', () => {
            const mockTarget = { tagName: 'INPUT' } as unknown as EventTarget;
            expect(isTypingInForm(mockTarget)).toBe(false);
        });
    });

    describe('Keyboard Event Handling Logic', () => {
        let mockKeysPressed: Set<string>;

        beforeEach(() => {
            mockKeysPressed = new Set();
        });

        function simulateKeyDown(code: string, target: EventTarget | null, canvasFocused: boolean): boolean {
            // Simulate the logic from GameCanvas handleKeyDown
            if (!canvasFocused || isTypingInForm(target)) {
                return false; // Event not captured by game
            }
            mockKeysPressed.add(code);
            return true; // Event captured by game (preventDefault called)
        }

        it('should capture keyboard input when canvas is focused and not typing in form', () => {
            const canvas = createMockElement('canvas');
            const captured = simulateKeyDown('KeyW', canvas, true);

            expect(captured).toBe(true);
            expect(mockKeysPressed.has('KeyW')).toBe(true);
        });

        it('should not capture keyboard input when canvas is not focused', () => {
            const canvas = createMockElement('canvas');
            const captured = simulateKeyDown('KeyW', canvas, false);

            expect(captured).toBe(false);
            expect(mockKeysPressed.has('KeyW')).toBe(false);
        });

        it('should not capture keyboard input when typing in input field', () => {
            const input = createMockElement('input', 'text');
            const captured = simulateKeyDown('KeyW', input, true);

            expect(captured).toBe(false);
            expect(mockKeysPressed.has('KeyW')).toBe(false);
        });

        it('should not capture keyboard input when typing in textarea', () => {
            const textarea = createMockElement('textarea');
            const captured = simulateKeyDown('KeyW', textarea, true);

            expect(captured).toBe(false);
            expect(mockKeysPressed.has('KeyW')).toBe(false);
        });

        it('should handle multiple key presses correctly', () => {
            const canvas = createMockElement('canvas');

            simulateKeyDown('KeyW', canvas, true);
            simulateKeyDown('KeyA', canvas, true);
            simulateKeyDown('KeyS', canvas, true);

            expect(mockKeysPressed.has('KeyW')).toBe(true);
            expect(mockKeysPressed.has('KeyA')).toBe(true);
            expect(mockKeysPressed.has('KeyS')).toBe(true);
            expect(mockKeysPressed.size).toBe(3);
        });

        it('should handle focus switching correctly', () => {
            const canvas = createMockElement('canvas');
            const input = createMockElement('input', 'text');

            // First, capture input when canvas is focused
            let captured = simulateKeyDown('KeyW', canvas, true);
            expect(captured).toBe(true);
            expect(mockKeysPressed.has('KeyW')).toBe(true);

            // Then, don't capture when typing in input
            captured = simulateKeyDown('KeyA', input, true);
            expect(captured).toBe(false);

            // Finally, capture again when back to canvas
            captured = simulateKeyDown('KeyS', canvas, true);
            expect(captured).toBe(true);
            expect(mockKeysPressed.has('KeyS')).toBe(true);
        });
    });

    describe('Integration Scenarios', () => {
        it('should handle task form interaction correctly', () => {
            // Simulate typing in task title field
            const taskInput = createMockElement('input', 'text');
            taskInput.setAttribute('placeholder', 'Task title');

            const captured = simulateKeyDown('KeyT', taskInput, true);
            expect(captured).toBe(false); // Should not interfere with typing
        });

        it('should handle special keys in forms correctly', () => {
            const input = createMockElement('input', 'text');

            // Test various special keys
            expect(simulateKeyDown('Enter', input, true)).toBe(false);
            expect(simulateKeyDown('Escape', input, true)).toBe(false);
            expect(simulateKeyDown('Tab', input, true)).toBe(false);
            expect(simulateKeyDown('Backspace', input, true)).toBe(false);
        });

        it('should maintain game functionality when not in forms', () => {
            const canvas = createMockElement('canvas');

            // Test game movement keys
            expect(simulateKeyDown('KeyW', canvas, true)).toBe(true); // Up
            expect(simulateKeyDown('KeyA', canvas, true)).toBe(true); // Left
            expect(simulateKeyDown('KeyS', canvas, true)).toBe(true); // Down
            expect(simulateKeyDown('KeyD', canvas, true)).toBe(true); // Right
            expect(simulateKeyDown('Space', canvas, true)).toBe(true); // Jump
        });
    });
});

function simulateKeyDown(code: string, target: EventTarget | null, canvasFocused: boolean): boolean {
    // Simulate the logic from GameCanvas handleKeyDown
    if (!canvasFocused || isTypingInForm(target)) {
        return false; // Event not captured by game
    }
    return true; // Event captured by game (preventDefault called)
}
