# Tileset Integration Guide

This guide explains how to use the tileset system in Banana Checklist to create game worlds with sprite tiles.

## Overview

The tileset system consists of two main components:
- **TilesetManager**: Loads and manages individual tiles from a sprite sheet
- **WorldBuilder**: Creates game worlds using tiles and handles positioning

## Getting Started

### 1. Basic Setup

The tileset system is already integrated into `GameCanvas.svelte`. Here's how it works:

```typescript
// Initialize the tileset manager
tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);

// Load the tileset
await tilesetManager.loadTileset('/assets/Tileset-Spritesheet.png');

// Create world builder
worldBuilder = new WorldBuilder(tilesetManager);
```

### 2. Current Implementation

The game currently creates a basic world with:
- **Ground tiles**: Across the full screen width
- **Dirt layers**: Below the ground for depth
- **Floating platforms**: At different heights for gameplay variety

### 3. Tileset Configuration

The current configuration assumes 16x16 pixel tiles:

```typescript
export const JUNGLE_TILESET_CONFIG: TilesetConfig = {
  tileWidth: 16,
  tileHeight: 16,
  tilesPerRow: 16,
  totalTiles: 256
};
```

## Customizing Your World

### Adding New Tiles

To add specific tiles to your world, you need to know their tile IDs. The system automatically extracts tiles from left to right, top to bottom:

```typescript
// Tile ID 0 = top-left tile
// Tile ID 1 = second tile from left in top row
// Tile ID 16 = first tile in second row (if tilesPerRow = 16)
```

### Creating Platforms

```typescript
// Create a 5-tile platform at position (200, 300) with 2x scaling
worldBuilder.createPlatform(tileId, 200, 300, 5, 2);
```

### Adding Individual Tiles

```typescript
// Add a single tile at specific coordinates
worldBuilder.addTile(tileId, x, y, scale);
```

### Clearing and Rebuilding

```typescript
// Clear the current world
worldBuilder.clearWorld();

// Rebuild with new layout
createBasicWorld();
```

## Tile ID Reference

You'll need to examine your `Tileset-Spritesheet.png` to determine which tile IDs correspond to which graphics. The current system uses:

- **Tile 0**: First tile (top-left) - used for grass
- **Tile 1**: Second tile - used for dirt
- **Tile 2**: Third tile - used for platforms

## Advanced Features

### Collision Detection

Get tiles in a specific area for collision detection:

```typescript
const tilesInArea = worldBuilder.getTilesInArea(x, y, width, height);
```

### Dynamic World Updates

The world automatically rebuilds when the screen is resized, ensuring tiles cover the full screen width.

### Performance Considerations

- Tiles are scaled 2x (16x16 becomes 32x32) to match the monkey sprite scaling
- The system creates enough ground tiles to cover the screen width plus extras for smooth scrolling
- Tiles are managed efficiently through PIXI containers

## Next Steps

### 1. Examine Your Tileset

Open `static/assets/Tileset-Spritesheet.png` in an image editor to see the available tiles and plan your world layout.

### 2. Update Tile IDs

Modify the `createBasicWorld()` function in `GameCanvas.svelte` to use appropriate tile IDs for your desired graphics:

```typescript
const grassTileId = 5; // Update to actual grass tile ID
const stoneTileId = 12; // Update to actual stone tile ID
const platformTileId = 8; // Update to actual platform tile ID
```

### 3. Create Custom Layouts

Add new functions to create different world layouts:

```typescript
function createJungleLevel() {
  // Create trees, vines, and jungle platforms
}

function createCaveLevel() {
  // Create stone walls and cave platforms
}
```

### 4. Add Collision Detection

Integrate tile-based collision detection with the monkey movement system for more realistic platforming.

## Testing

The tileset system includes comprehensive tests in `src/lib/utils/tileUtils.test.ts` covering:
- Tileset loading and extraction
- Tile sprite creation
- World building operations
- Error handling

Run tests with: `npm test -- --run src/lib/utils/tileUtils.test.ts`

## Troubleshooting

### Common Issues

1. **Tiles not appearing**: Check that the tileset path is correct and the file exists
2. **Wrong tile graphics**: Verify tile IDs match your tileset layout
3. **Scaling issues**: Ensure scale parameter matches your sprite scaling (usually 2x)
4. **Performance problems**: Consider reducing the number of tiles or using tile pooling for large worlds

### Debug Information

The system logs helpful information to the console:
- Tileset dimensions when loaded
- Number of tiles extracted
- World creation progress

Check the browser console for these messages when debugging.
