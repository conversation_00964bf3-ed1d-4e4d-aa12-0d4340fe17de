import * as PIXI from 'pixi.js';
import type { NPC, NPCDialogue, NPCShop } from '../types';

interface NPCSprite {
    npc: NPC;
    sprite: PIXI.Graphics | PIXI.AnimatedSprite;
    interactionZone: PIXI.Graphics;
    isPlayerNearby: boolean;
}

export class NPCManager {
    private npcs: Map<string, NPCSprite> = new Map();
    private container: PIXI.Container;
    private app: PIXI.Application;
    private onInteractionCallback?: (npc: NPC) => void;

    constructor(app: PIXI.Application) {
        this.app = app;
        this.container = new PIXI.Container();
        this.container.zIndex = 80; // NPCs render in front of world but behind monkey
        app.stage.addChild(this.container);
    }

    /**
     * Add an NPC to the scene
     */
    addNPC(npc: NPC): void {
        // Create NPC sprite based on type
        const sprite = this.createNPCSprite(npc);

        // Create interaction zone (invisible circle around NPC)
        const interactionZone = new PIXI.Graphics();
        interactionZone.beginFill(0x00ff00, 0.0); // Transparent green
        interactionZone.drawCircle(0, 0, 50); // 50px radius interaction zone
        interactionZone.endFill();
        interactionZone.x = npc.position.x;
        interactionZone.y = npc.position.y;
        interactionZone.interactive = true;
        interactionZone.buttonMode = true;

        // Add click interaction
        interactionZone.on('pointerdown', () => {
            this.handleNPCInteraction(npc);
        });

        const npcSprite: NPCSprite = {
            npc,
            sprite,
            interactionZone,
            isPlayerNearby: false
        };

        this.npcs.set(npc.id, npcSprite);
        this.container.addChild(sprite);
        this.container.addChild(interactionZone);
    }

    /**
     * Create a sprite for the NPC based on its type
     */
    private createNPCSprite(npc: NPC): PIXI.Graphics {
        const sprite = new PIXI.Graphics();

        // Create a simple bot representation
        if (npc.type === 'vendor' && npc.id === 'banana-bot') {
            // Bot body (rectangle)
            sprite.beginFill(0x4a4a4a); // Dark gray
            sprite.drawRoundedRect(-16, -20, 32, 40, 4);
            sprite.endFill();

            // Bot head (smaller rectangle)
            sprite.beginFill(0x6a6a6a); // Lighter gray
            sprite.drawRoundedRect(-12, -32, 24, 16, 2);
            sprite.endFill();

            // Eyes (small circles)
            sprite.beginFill(0x00ff00); // Green eyes
            sprite.drawCircle(-6, -26, 2);
            sprite.drawCircle(6, -26, 2);
            sprite.endFill();

            // Banana symbol on chest
            sprite.beginFill(0xffff00); // Yellow
            sprite.drawEllipse(0, -8, 4, 8);
            sprite.endFill();

            // Simple antenna
            sprite.lineStyle(2, 0x888888);
            sprite.moveTo(0, -32);
            sprite.lineTo(0, -40);
            sprite.lineStyle(0);

            // Antenna tip
            sprite.beginFill(0xff0000); // Red
            sprite.drawCircle(0, -40, 2);
            sprite.endFill();
        } else {
            // Default NPC representation
            sprite.beginFill(0x8B4513); // Brown
            sprite.drawCircle(0, 0, 16);
            sprite.endFill();
        }

        // Position the sprite
        sprite.x = npc.position.x;
        sprite.y = npc.position.y;
        sprite.scale.set(2, 2); // Match monkey scaling

        return sprite;
    }

    /**
     * Handle NPC interaction
     */
    private handleNPCInteraction(npc: NPC): void {
        console.log(`Interacting with ${npc.name}`);

        // Trigger interaction callback if set
        if (this.onInteractionCallback) {
            this.onInteractionCallback(npc);
        }
    }

    /**
     * Set callback for NPC interactions
     */
    setInteractionCallback(callback: (npc: NPC) => void): void {
        this.onInteractionCallback = callback;
    }

    /**
     * Update NPC states (called from game loop)
     */
    update(monkeyPosition: { x: number; y: number }): void {
        this.npcs.forEach((npcSprite) => {
            const distance = Math.sqrt(
                Math.pow(monkeyPosition.x - npcSprite.npc.position.x, 2) +
                Math.pow(monkeyPosition.y - npcSprite.npc.position.y, 2)
            );

            const wasNearby = npcSprite.isPlayerNearby;
            npcSprite.isPlayerNearby = distance < 60; // 60px proximity

            // Add visual feedback when player is nearby
            if (npcSprite.isPlayerNearby && !wasNearby) {
                // Player approached - add glow effect (if available)
                try {
                    if (PIXI.filters && PIXI.filters.GlowFilter) {
                        npcSprite.sprite.filters = [new PIXI.filters.GlowFilter({
                            distance: 10,
                            outerStrength: 2,
                            color: 0xffff00
                        })];
                    }
                } catch (error) {
                    // Glow filter not available, skip visual effect
                    console.warn('Glow filter not available:', error);
                }
            } else if (!npcSprite.isPlayerNearby && wasNearby) {
                // Player left - remove glow effect
                npcSprite.sprite.filters = [];
            }
        });
    }

    /**
     * Remove an NPC from the scene
     */
    removeNPC(npcId: string): void {
        const npcSprite = this.npcs.get(npcId);
        if (npcSprite) {
            this.container.removeChild(npcSprite.sprite);
            this.container.removeChild(npcSprite.interactionZone);
            this.npcs.delete(npcId);
        }
    }

    /**
     * Clear all NPCs
     */
    clearAllNPCs(): void {
        this.npcs.forEach((npcSprite) => {
            this.container.removeChild(npcSprite.sprite);
            this.container.removeChild(npcSprite.interactionZone);
        });
        this.npcs.clear();
    }

    /**
     * Get the container for z-index management
     */
    getContainer(): PIXI.Container {
        return this.container;
    }
}

/**
 * Create the Banana Bot NPC data
 */
export function createBananaBotNPC(farmPosition: { x: number; y: number }): NPC {
    const dialogues: NPCDialogue[] = [
        {
            id: 'greeting',
            text: "🤖 Beep boop! Welcome to the Banana Farm! I'm your friendly Banana Bot assistant.",
            responses: [
                { text: "Tell me about the farm", action: 'dialogue', target: 'farm-info' },
                { text: "Show me upgrades", action: 'shop' },
                { text: "Thanks, bye!", action: 'close' }
            ]
        },
        {
            id: 'farm-info',
            text: "🍌 This farm generates bananas automatically! Upgrade my systems to increase production rates and unlock new features.",
            responses: [
                { text: "Show me upgrades", action: 'shop' },
                { text: "How does offline generation work?", action: 'dialogue', target: 'offline-info' },
                { text: "Got it, thanks!", action: 'close' }
            ]
        },
        {
            id: 'offline-info',
            text: "⏰ Even when you're away, I keep working! When you return, I'll calculate how many bananas were generated while you were gone.",
            responses: [
                { text: "Show me upgrades", action: 'shop' },
                { text: "Back to main menu", action: 'dialogue', target: 'greeting' },
                { text: "Awesome!", action: 'close' }
            ]
        }
    ];

    const shop: NPCShop = {
        currency: 'bananas',
        items: [
            {
                id: 'bot-upgrade-1',
                name: 'Basic Bot Upgrade',
                description: 'Increase generation rate to 1 banana/minute',
                cost: 500,
                effect: 'generation_rate',
                maxLevel: 1,
                currentLevel: 0,
                category: 'bot'
            },
            {
                id: 'plantation-upgrade-1',
                name: 'Small Plot Expansion',
                description: 'Expand farm size for +50% generation',
                cost: 2000,
                effect: 'generation_multiplier',
                maxLevel: 1,
                currentLevel: 0,
                category: 'plantation'
            }
        ]
    };

    return {
        id: 'banana-bot',
        name: 'Banana Bot',
        type: 'vendor',
        position: farmPosition,
        spriteSheet: 'custom', // We're using custom graphics
        dialogues,
        shop
    };
}
