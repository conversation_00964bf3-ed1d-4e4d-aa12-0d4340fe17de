import{b as d,u as g,d as c,r as i,e as b,g as m,h as p,i as h,j as k,k as v}from"./QsgQHiSo.js";function x(n=!1){const s=d,e=s.l.u;if(!e)return;let f=()=>h(s.s);if(n){let a=0,t={};const _=k(()=>{let l=!1;const r=s.s;for(const o in r)r[o]!==t[o]&&(t[o]=r[o],l=!0);return l&&a++,a});f=()=>p(_)}e.b.length&&g(()=>{u(s,f),i(e.b)}),c(()=>{const a=b(()=>e.m.map(m));return()=>{for(const t of a)typeof t=="function"&&t()}}),e.a.length&&c(()=>{u(s,f),i(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}v();export{x as i};
