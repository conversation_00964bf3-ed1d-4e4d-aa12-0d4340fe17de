import{v as C,w as I,x as Y,E as q,y as j,H as k,z as F,A as H,B as Z,C as A,D as L,F as y,G as O,U as p,I as z,J as G,K as M,e as B,L as W,S as D,M as J,N as K,O as N,P as Q,Q as $,h as P,R as V,T as X,V as x,j as ee,W as re,X as te,Y as ne,Z as se,_ as ie,$ as ae,a0 as ue,a1 as fe}from"./QsgQHiSo.js";import{a as le,g as ce}from"./C_1qu2iM.js";function Se(e,r,[n,t]=[0,0]){I&&n===0&&Y();var i=e,s=null,l=null,a=p,v=n>0?q:0,h=!1;const u=(c,b=!0)=>{h=!0,_(b,c)},_=(c,b)=>{if(a===(a=c))return;let f=!1;if(I&&t!==-1){if(n===0){const o=j(i);o===k?t=0:o===F?t=1/0:(t=parseInt(o.substring(1)),t!==t&&(t=a?1/0:-1))}const S=t>n;!!a===S&&(i=H(),Z(i),A(!1),f=!0,t=-1)}a?(s?L(s):b&&(s=y(()=>b(i))),l&&O(l,()=>{l=null})):(l?L(l):b&&(l=y(()=>b(i,[n+1,t]))),s&&O(s,()=>{s=null})),f&&A(!0)};C(()=>{h=!1,r(u),h||_(null,null)},v),I&&(i=z)}function w(e,r){return e===r||(e==null?void 0:e[D])===r}function Ee(e={},r,n,t){return G(()=>{var i,s;return M(()=>{i=s,s=[],B(()=>{e!==n(...s)&&(r(e,...s),i&&w(n(...i),e)&&r(null,...i))})}),()=>{W(()=>{s&&w(n(...s),e)&&r(null,...s)})}}),e}let g=!1,R=Symbol();function ge(e,r,n){const t=n[r]??(n[r]={store:null,source:Q(void 0),unsubscribe:N});if(t.store!==e&&!(R in n))if(t.unsubscribe(),t.store=e??null,e==null)t.source.v=void 0,t.unsubscribe=N;else{var i=!0;t.unsubscribe=le(e,s=>{i?t.source.v=s:$(t.source,s)}),i=!1}return e&&R in n?ce(e):P(t.source)}function Pe(){const e={};function r(){J(()=>{for(var n in e)e[n].unsubscribe();K(e,R,{enumerable:!1,value:!0})})}return[e,r]}function oe(e){var r=g;try{return g=!1,[e(),g]}finally{g=r}}function de(e){var r;return((r=e.ctx)==null?void 0:r.d)??!1}function Ie(e,r,n,t){var m;var i=!se||(n&ie)!==0,s=(n&ne)!==0,l=(n&ue)!==0,a=t,v=!0,h=()=>(v&&(v=!1,a=l?B(t):t),a),u;if(s){var _=D in e||fe in e;u=((m=V(e,r))==null?void 0:m.set)??(_&&r in e?d=>e[r]=d:void 0)}var c,b=!1;s?[c,b]=oe(()=>e[r]):c=e[r],c===void 0&&t!==void 0&&(c=h(),u&&(i&&X(),u(c)));var f;if(i?f=()=>{var d=e[r];return d===void 0?h():(v=!0,d)}:f=()=>{var d=e[r];return d!==void 0&&(a=void 0),d===void 0?a:d},i&&(n&x)===0)return f;if(u){var S=e.$$legacy;return function(d,E){return arguments.length>0?((!i||!E||S||b)&&u(E?f():d),d):f()}}var o=((n&ae)!==0?ee:re)(f);return s&&P(o),function(d,E){if(arguments.length>0){const T=E?P(o):i&&s?te(d):d;return $(o,T),a!==void 0&&(a=T),d}return de(o)?o.v:P(o)}}const ve="modulepreload",_e=function(e,r){return new URL(e,r).href},U={},Re=function(r,n,t){let i=Promise.resolve();if(n&&n.length>0){let l=function(u){return Promise.all(u.map(_=>Promise.resolve(_).then(c=>({status:"fulfilled",value:c}),c=>({status:"rejected",reason:c}))))};const a=document.getElementsByTagName("link"),v=document.querySelector("meta[property=csp-nonce]"),h=(v==null?void 0:v.nonce)||(v==null?void 0:v.getAttribute("nonce"));i=l(n.map(u=>{if(u=_e(u,t),u in U)return;U[u]=!0;const _=u.endsWith(".css"),c=_?'[rel="stylesheet"]':"";if(!!t)for(let S=a.length-1;S>=0;S--){const o=a[S];if(o.href===u&&(!_||o.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${c}`))return;const f=document.createElement("link");if(f.rel=_?"stylesheet":ve,_||(f.as="script"),f.crossOrigin="",f.href=u,h&&f.setAttribute("nonce",h),document.head.appendChild(f),_)return new Promise((S,o)=>{f.addEventListener("load",S),f.addEventListener("error",()=>o(new Error(`Unable to preload CSS for ${u}`)))})}))}function s(l){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=l,window.dispatchEvent(a),!a.defaultPrevented)throw l}return i.then(l=>{for(const a of l||[])a.status==="rejected"&&s(a.reason);return r().catch(s)})};export{Re as _,ge as a,Ee as b,Se as i,Ie as p,Pe as s};
