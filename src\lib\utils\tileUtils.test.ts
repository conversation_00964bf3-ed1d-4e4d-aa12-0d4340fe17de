import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TilesetManager, WorldBuilder, JUNGLE_TILESET_CONFIG, COMMON_TILES } from './tileUtils';
import * as PIXI from 'pixi.js';

// Mock PIXI
vi.mock('pixi.js', () => ({
  Assets: {
    load: vi.fn()
  },
  Texture: vi.fn().mockImplementation(() => ({
    width: 256,
    height: 256,
    source: { width: 256, height: 256 }
  })),
  Rectangle: vi.fn(),
  Sprite: vi.fn().mockImplementation(() => ({
    scale: { set: vi.fn() },
    x: 0,
    y: 0,
    width: 16,
    height: 16
  })),
  Container: vi.fn().mockImplementation(() => ({
    addChild: vi.fn(),
    removeChildren: vi.fn()
  }))
}));

describe('TilesetManager', () => {
  let tilesetManager: TilesetManager;
  let mockTexture: any;

  beforeEach(() => {
    tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);
    
    mockTexture = {
      width: 256,
      height: 256,
      source: { width: 256, height: 256 }
    };

    vi.mocked(PIXI.Assets.load).mockResolvedValue(mockTexture);
    vi.mocked(PIXI.Texture).mockImplementation(() => mockTexture);
  });

  it('should create TilesetManager with correct config', () => {
    expect(tilesetManager).toBeDefined();
    expect(tilesetManager.isLoaded()).toBe(false);
  });

  it('should load tileset successfully', async () => {
    await tilesetManager.loadTileset('/test/tileset.png');
    
    expect(PIXI.Assets.load).toHaveBeenCalledWith('/test/tileset.png');
    expect(tilesetManager.isLoaded()).toBe(true);
  });

  it('should extract tiles from tileset', async () => {
    await tilesetManager.loadTileset('/test/tileset.png');
    
    const availableTiles = tilesetManager.getAvailableTileIds();
    expect(availableTiles.length).toBeGreaterThan(0);
  });

  it('should get tile by ID', async () => {
    await tilesetManager.loadTileset('/test/tileset.png');
    
    const tile = tilesetManager.getTile(0);
    expect(tile).toBeDefined();
  });

  it('should return null for invalid tile ID', async () => {
    await tilesetManager.loadTileset('/test/tileset.png');
    
    const tile = tilesetManager.getTile(9999);
    expect(tile).toBeNull();
  });

  it('should create tile sprite', async () => {
    await tilesetManager.loadTileset('/test/tileset.png');
    
    const sprite = tilesetManager.createTileSprite(0, 2);
    expect(sprite).toBeDefined();
    expect(PIXI.Sprite).toHaveBeenCalled();
  });

  it('should return null for invalid tile sprite creation', async () => {
    await tilesetManager.loadTileset('/test/tileset.png');
    
    const sprite = tilesetManager.createTileSprite(9999);
    expect(sprite).toBeNull();
  });

  it('should handle loading errors', async () => {
    vi.mocked(PIXI.Assets.load).mockRejectedValue(new Error('Load failed'));
    
    await expect(tilesetManager.loadTileset('/invalid/path.png')).rejects.toThrow('Load failed');
  });
});

describe('WorldBuilder', () => {
  let tilesetManager: TilesetManager;
  let worldBuilder: WorldBuilder;
  let mockContainer: any;

  beforeEach(async () => {
    tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);
    
    const mockTexture = {
      width: 256,
      height: 256,
      source: { width: 256, height: 256 }
    };

    mockContainer = {
      addChild: vi.fn(),
      removeChildren: vi.fn()
    };

    vi.mocked(PIXI.Assets.load).mockResolvedValue(mockTexture);
    vi.mocked(PIXI.Texture).mockImplementation(() => mockTexture);
    vi.mocked(PIXI.Container).mockImplementation(() => mockContainer);

    await tilesetManager.loadTileset('/test/tileset.png');
    worldBuilder = new WorldBuilder(tilesetManager);
  });

  it('should create WorldBuilder', () => {
    expect(worldBuilder).toBeDefined();
    expect(worldBuilder.getContainer()).toBeDefined();
  });

  it('should add tile to world', () => {
    const sprite = worldBuilder.addTile(0, 100, 200, 2);
    
    expect(sprite).toBeDefined();
    expect(mockContainer.addChild).toHaveBeenCalled();
  });

  it('should create platform', () => {
    const sprites = worldBuilder.createPlatform(0, 100, 200, 5, 2);
    
    expect(sprites).toHaveLength(5);
    expect(mockContainer.addChild).toHaveBeenCalledTimes(5);
  });

  it('should clear world', () => {
    worldBuilder.addTile(0, 100, 200);
    worldBuilder.clearWorld();
    
    expect(mockContainer.removeChildren).toHaveBeenCalled();
    expect(worldBuilder.getWorldTiles()).toHaveLength(0);
  });

  it('should get tiles in area', () => {
    // Add some tiles
    worldBuilder.addTile(0, 100, 200);
    worldBuilder.addTile(0, 150, 200);
    worldBuilder.addTile(0, 300, 200);
    
    const tilesInArea = worldBuilder.getTilesInArea(90, 190, 100, 50);
    expect(tilesInArea.length).toBeGreaterThan(0);
  });

  it('should handle invalid tile creation', () => {
    const sprite = worldBuilder.addTile(9999, 100, 200);
    expect(sprite).toBeNull();
  });

  it('should handle platform creation with invalid tile', () => {
    const sprites = worldBuilder.createPlatform(9999, 100, 200, 3);
    expect(sprites).toHaveLength(0);
  });
});

describe('JUNGLE_TILESET_CONFIG', () => {
  it('should have correct default values', () => {
    expect(JUNGLE_TILESET_CONFIG.tileWidth).toBe(16);
    expect(JUNGLE_TILESET_CONFIG.tileHeight).toBe(16);
    expect(JUNGLE_TILESET_CONFIG.tilesPerRow).toBe(16);
    expect(JUNGLE_TILESET_CONFIG.totalTiles).toBe(256);
  });
});

describe('COMMON_TILES', () => {
  it('should have expected tile constants', () => {
    expect(COMMON_TILES.GRASS_TOP).toBe(0);
    expect(COMMON_TILES.GRASS_MIDDLE).toBe(1);
    expect(COMMON_TILES.DIRT).toBe(2);
    expect(typeof COMMON_TILES.STONE).toBe('number');
    expect(typeof COMMON_TILES.PLATFORM_LEFT).toBe('number');
  });
});
