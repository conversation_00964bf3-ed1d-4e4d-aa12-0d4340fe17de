# Assets Directory

This directory contains all game assets for Banana Checklist.

## Structure

- `sprites/` - Character and object sprites
  - `monkey/` - Monkey character animations
  - `bananas/` - Banana collectible sprites
  - `trees/` - Task tree sprites
  - `ui/` - UI element sprites

- `tilesets/` - Environment tilesets
  - `jungle/` - Jungle environment tiles
  - `platforms/` - Platform and structure tiles

- `audio/` - Sound effects and music
  - `sfx/` - Sound effects
  - `music/` - Background music

- `fonts/` - Custom fonts for retro styling

## Asset Sources

The project uses the free tileset from:
https://pixelsym.itch.io/pixel-jungle-monkey-platformer-beginner-friendly

## File Formats

- Sprites: PNG with transparency
- Tilesets: PNG sprite sheets
- Audio: OGG/MP3 for web compatibility
- Fonts: WOFF2/TTF

## Naming Convention

- Use kebab-case for file names
- Include animation frame numbers: `monkey-walk-01.png`
- Use descriptive names: `banana-golden.png`
