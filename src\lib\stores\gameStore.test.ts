import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { gameStore, inputStore, GAME_UPGRADES, purchasedUpgrades, availableUpgrades } from './gameStore';
import type { GameUpgrade } from '../types';

describe('gameStore', () => {
    beforeEach(() => {
        // Reset store to default state
        gameStore.set({
            monkeyPosition: { x: 100, y: 500 },
            currentScene: 'jungle',
            unlockedAreas: ['starting-grove'],
            cosmetics: {
                monkeySkin: 'default',
                theme: 'jungle'
            },
            upgrades: []
        });
    });

    describe('initial state', () => {
        it('should have correct default state', () => {
            const state = get(gameStore);
            expect(state.monkeyPosition).toEqual({ x: 100, y: 500 });
            expect(state.currentScene).toBe('jungle');
            expect(state.unlockedAreas).toEqual(['starting-grove']);
            expect(state.cosmetics.monkeySkin).toBe('default');
            expect(state.cosmetics.theme).toBe('jungle');
            expect(state.upgrades).toEqual([]);
        });
    });

    describe('updateMonkeyPosition', () => {
        it('should update monkey position', () => {
            gameStore.updateMonkeyPosition(200, 300);
            const state = get(gameStore);
            expect(state.monkeyPosition).toEqual({ x: 200, y: 300 });
        });

        it('should preserve other state when updating position', () => {
            const initialState = get(gameStore);
            gameStore.updateMonkeyPosition(150, 250);
            const newState = get(gameStore);

            expect(newState.currentScene).toBe(initialState.currentScene);
            expect(newState.unlockedAreas).toEqual(initialState.unlockedAreas);
            expect(newState.cosmetics).toEqual(initialState.cosmetics);
        });
    });

    describe('changeScene', () => {
        it('should change current scene', () => {
            gameStore.changeScene('grove');
            const state = get(gameStore);
            expect(state.currentScene).toBe('grove');
        });

        it('should change to minigame scene', () => {
            gameStore.changeScene('minigame');
            const state = get(gameStore);
            expect(state.currentScene).toBe('minigame');
        });
    });

    describe('unlockArea', () => {
        it('should add new area to unlocked areas', () => {
            gameStore.unlockArea('secret-grove');
            const state = get(gameStore);
            expect(state.unlockedAreas).toContain('secret-grove');
            expect(state.unlockedAreas).toContain('starting-grove');
        });

        it('should handle multiple area unlocks', () => {
            gameStore.unlockArea('area1');
            gameStore.unlockArea('area2');
            const state = get(gameStore);
            expect(state.unlockedAreas).toEqual(['starting-grove', 'area1', 'area2']);
        });
    });

    describe('updateCosmetics', () => {
        it('should update monkey skin', () => {
            gameStore.updateCosmetics({ monkeySkin: 'golden' });
            const state = get(gameStore);
            expect(state.cosmetics.monkeySkin).toBe('golden');
            expect(state.cosmetics.theme).toBe('jungle'); // Should preserve other cosmetics
        });

        it('should update theme', () => {
            gameStore.updateCosmetics({ theme: 'desert' });
            const state = get(gameStore);
            expect(state.cosmetics.theme).toBe('desert');
            expect(state.cosmetics.monkeySkin).toBe('default'); // Should preserve other cosmetics
        });

        it('should update multiple cosmetics at once', () => {
            gameStore.updateCosmetics({
                monkeySkin: 'silver',
                theme: 'cave',
                hat: 'crown'
            });
            const state = get(gameStore);
            expect(state.cosmetics.monkeySkin).toBe('silver');
            expect(state.cosmetics.theme).toBe('cave');
            expect(state.cosmetics.hat).toBe('crown');
        });
    });

    describe('purchaseUpgrade', () => {
        it('should add upgrade to purchased upgrades', () => {
            const upgrade: GameUpgrade = {
                id: 'test-upgrade',
                name: 'Test Upgrade',
                description: 'Test description',
                cost: 100,
                purchased: false,
                effect: 'test_effect'
            };

            gameStore.purchaseUpgrade(upgrade);
            const state = get(gameStore);

            expect(state.upgrades).toHaveLength(1);
            expect(state.upgrades[0].id).toBe('test-upgrade');
            expect(state.upgrades[0].purchased).toBe(true);
        });

        it('should handle multiple upgrade purchases', () => {
            const upgrade1: GameUpgrade = {
                id: 'upgrade1',
                name: 'Upgrade 1',
                description: 'First upgrade',
                cost: 100,
                purchased: false,
                effect: 'effect1'
            };

            const upgrade2: GameUpgrade = {
                id: 'upgrade2',
                name: 'Upgrade 2',
                description: 'Second upgrade',
                cost: 200,
                purchased: false,
                effect: 'effect2'
            };

            gameStore.purchaseUpgrade(upgrade1);
            gameStore.purchaseUpgrade(upgrade2);

            const state = get(gameStore);
            expect(state.upgrades).toHaveLength(2);
            expect(state.upgrades.every(u => u.purchased)).toBe(true);
        });
    });
});

describe('inputStore', () => {
    beforeEach(() => {
        inputStore.reset();
    });

    describe('initial state', () => {
        it('should have all keys set to false', () => {
            const input = get(inputStore);
            expect(input.left).toBe(false);
            expect(input.right).toBe(false);
            expect(input.up).toBe(false);
            expect(input.down).toBe(false);
            expect(input.jump).toBe(false);
            expect(input.interact).toBe(false);
        });
    });

    describe('setKey', () => {
        it('should set individual keys to true', () => {
            inputStore.setKey('left', true);
            const input = get(inputStore);
            expect(input.left).toBe(true);
            expect(input.right).toBe(false); // Other keys should remain unchanged
        });

        it('should set individual keys to false', () => {
            inputStore.setKey('jump', true);
            inputStore.setKey('jump', false);
            const input = get(inputStore);
            expect(input.jump).toBe(false);
        });

        it('should handle multiple key presses', () => {
            inputStore.setKey('left', true);
            inputStore.setKey('jump', true);
            inputStore.setKey('interact', true);

            const input = get(inputStore);
            expect(input.left).toBe(true);
            expect(input.jump).toBe(true);
            expect(input.interact).toBe(true);
            expect(input.right).toBe(false);
            expect(input.up).toBe(false);
            expect(input.down).toBe(false);
        });
    });

    describe('reset', () => {
        it('should reset all keys to false', () => {
            // Set some keys to true
            inputStore.setKey('left', true);
            inputStore.setKey('jump', true);
            inputStore.setKey('interact', true);

            // Reset
            inputStore.reset();

            const input = get(inputStore);
            expect(input.left).toBe(false);
            expect(input.right).toBe(false);
            expect(input.up).toBe(false);
            expect(input.down).toBe(false);
            expect(input.jump).toBe(false);
            expect(input.interact).toBe(false);
        });
    });
});

describe('GAME_UPGRADES', () => {
    it('should have correct upgrade definitions', () => {
        expect(GAME_UPGRADES).toHaveLength(4);

        const upgradeIds = GAME_UPGRADES.map(u => u.id);
        expect(upgradeIds).toContain('faster-monkey');
        expect(upgradeIds).toContain('banana-bots');
        expect(upgradeIds).toContain('double-jump');
        expect(upgradeIds).toContain('banana-magnet');
    });

    it('should have all upgrades initially unpurchased', () => {
        const allUnpurchased = GAME_UPGRADES.every(upgrade => !upgrade.purchased);
        expect(allUnpurchased).toBe(true);
    });

    it('should have valid cost values', () => {
        const allHaveValidCosts = GAME_UPGRADES.every(upgrade =>
            typeof upgrade.cost === 'number' && upgrade.cost > 0
        );
        expect(allHaveValidCosts).toBe(true);
    });
});

describe('purchasedUpgrades derived store', () => {
    beforeEach(() => {
        gameStore.set({
            monkeyPosition: { x: 100, y: 500 },
            currentScene: 'jungle',
            unlockedAreas: ['starting-grove'],
            cosmetics: { monkeySkin: 'default', theme: 'jungle' },
            upgrades: []
        });
    });

    it('should return empty array when no upgrades purchased', () => {
        const purchased = get(purchasedUpgrades);
        expect(purchased).toEqual([]);
    });

    it('should return only purchased upgrades', () => {
        const upgrade1: GameUpgrade = {
            id: 'upgrade1',
            name: 'Upgrade 1',
            description: 'First upgrade',
            cost: 100,
            purchased: true,
            effect: 'effect1'
        };

        const upgrade2: GameUpgrade = {
            id: 'upgrade2',
            name: 'Upgrade 2',
            description: 'Second upgrade',
            cost: 200,
            purchased: false,
            effect: 'effect2'
        };

        gameStore.set({
            monkeyPosition: { x: 100, y: 500 },
            currentScene: 'jungle',
            unlockedAreas: ['starting-grove'],
            cosmetics: { monkeySkin: 'default', theme: 'jungle' },
            upgrades: [upgrade1, upgrade2]
        });

        const purchased = get(purchasedUpgrades);
        expect(purchased).toHaveLength(1);
        expect(purchased[0].id).toBe('upgrade1');
    });
});

describe('availableUpgrades derived store', () => {
    beforeEach(() => {
        gameStore.set({
            monkeyPosition: { x: 100, y: 500 },
            currentScene: 'jungle',
            unlockedAreas: ['starting-grove'],
            cosmetics: { monkeySkin: 'default', theme: 'jungle' },
            upgrades: []
        });
    });

    it('should return all upgrades when none purchased', () => {
        const available = get(availableUpgrades);
        expect(available).toHaveLength(GAME_UPGRADES.length);
    });

    it('should exclude purchased upgrades', () => {
        // Purchase the first upgrade
        gameStore.purchaseUpgrade(GAME_UPGRADES[0]);

        const available = get(availableUpgrades);
        expect(available).toHaveLength(GAME_UPGRADES.length - 1);
        expect(available.find(u => u.id === GAME_UPGRADES[0].id)).toBeUndefined();
    });

    it('should return empty array when all upgrades purchased', () => {
        // Purchase all upgrades
        GAME_UPGRADES.forEach(upgrade => {
            gameStore.purchaseUpgrade(upgrade);
        });

        const available = get(availableUpgrades);
        expect(available).toEqual([]);
    });
});
