# Product Requirements Document (PRD): Banana Farm & Bot NPC

**Version:** 1.0  
**Date:** July 7, 2025  
**Author:** Augment Agent  
**Project Name:** Banana Checklist - Banana Farm Feature  
**Target Platforms:** Web, iOS, Android  
**Tech Stack:** TypeScript, PixiJS, SvelteKit

---

## 1. Product Overview

### 1.1 Purpose

This PRD outlines the implementation of a new **Banana Farm scene** accessible by moving offscreen to the right of the main jungle scene. The farm will feature a **Banana Bot NPC character** that serves as the interface for idle/incremental game mechanics, allowing players to purchase and manage automated banana generation systems.

### 1.2 Strategic Alignment

This feature directly implements the Phase 2 idle-game elements described in `phase-2-ideas.md`, specifically:
- Passive banana generation through "Banana Bots"
- Building-based incremental mechanics (banana farm)
- NPC-driven upgrade system
- Extended world exploration through scene transitions

---

## 2. Feature Requirements

### 2.1 Core Features

#### 2.1.1 Banana Farm Scene
- **New Scene Type:** Add `'banana-farm'` to the `GameState.currentScene` union type
- **Scene Transition:** Triggered when monkey moves beyond the right edge of the main jungle scene
- **Visual Design:** Farm-themed environment using existing tileset system
  - Farmland tiles for ground
  - Banana tree sprites in organized rows
  - Farm buildings/structures for visual depth
  - Rustic/agricultural aesthetic distinct from jungle theme

#### 2.1.2 Banana Bot NPC Character
- **Character Design:** Friendly robot monkey with banana-themed elements
  - 32x32 pixel sprite matching existing monkey scale (2x scaling)
  - Idle animation cycle
  - Interaction animation when player approaches
- **Positioning:** Central location in farm scene, easily discoverable
- **Interaction System:** Click/tap or proximity-based interaction trigger

#### 2.1.3 Idle Generation System
- **Passive Income:** Banana generation continues when app is closed
- **Offline Calculation:** Calculate bananas earned during offline time
- **Rate Scaling:** Multiple upgrade tiers for generation speed
- **Visual Feedback:** Animated banana collection when returning to game

### 2.2 Upgrade System Integration

#### 2.2.1 Enhanced Banana Bots Upgrade
- **Existing Integration:** Leverage current "Banana Bots" upgrade from `gameStore.ts`
- **Tiered System:** Multiple bot upgrade levels
  - Level 1: 1 banana/minute (existing 500 banana cost)
  - Level 2: 3 bananas/minute (1,500 banana cost)
  - Level 3: 6 bananas/minute (3,000 banana cost)
  - Level 4: 12 bananas/minute (6,000 banana cost)

#### 2.2.2 Farm Building Upgrades
- **Banana Plantation:** Increases base generation rate
  - Small Plot: +50% generation (2,000 bananas)
  - Medium Farm: +100% generation (5,000 bananas)
  - Mega Plantation: +200% generation (12,000 bananas)

### 2.3 User Interface Requirements

#### 2.3.1 NPC Interaction Dialog
- **Shop Interface:** Grid-based upgrade selection
- **Progress Display:** Current generation rate and next upgrade costs
- **Offline Report:** "While you were away..." summary modal
- **Visual Indicators:** Affordable upgrades highlighted in green

#### 2.3.2 Farm Scene UI Elements
- **Generation Counter:** Real-time banana/minute display
- **Bot Status:** Visual indicators of active bots working
- **Return Navigation:** Clear path back to main jungle scene

---

## 3. Technical Implementation

### 3.1 Type System Updates

#### 3.1.1 GameState Extensions
```typescript
// Update existing GameState interface
export interface GameState {
  monkeyPosition: { x: number; y: number };
  currentScene: 'jungle' | 'grove' | 'minigame' | 'banana-farm'; // Add new scene
  unlockedAreas: string[];
  farmData?: FarmState; // New optional farm state
  cosmetics: {
    monkeySkin: string;
    hat?: string;
    theme: string;
  };
  upgrades: GameUpgrade[];
}

// New farm-specific state
export interface FarmState {
  botLevel: number;
  plantationLevel: number;
  generationRate: number; // bananas per minute
  lastUpdateTime: number; // timestamp for offline calculation
  totalGenerated: number;
}
```

#### 3.1.2 NPC System Types
```typescript
export interface NPC {
  id: string;
  name: string;
  type: 'vendor' | 'quest-giver' | 'decorator';
  position: { x: number; y: number };
  spriteSheet: string;
  dialogues: NPCDialogue[];
  shop?: NPCShop;
}

export interface NPCShop {
  items: ShopItem[];
  currency: 'bananas' | 'coins';
}

export interface ShopItem {
  id: string;
  name: string;
  description: string;
  cost: number;
  effect: string;
  maxLevel: number;
  currentLevel: number;
}
```

### 3.2 Scene Management

#### 3.2.1 Scene Transition System
- **Trigger Zones:** Invisible collision areas at screen edges
- **Smooth Transitions:** Fade-out/fade-in effect between scenes
- **State Persistence:** Maintain monkey position relative to entry point
- **Loading Management:** Preload farm assets when approaching transition

#### 3.2.2 Farm Scene Creation
- **Tileset Integration:** Use existing `WorldBuilder` and `TilesetManager`
- **Asset Requirements:** 
  - Farm-themed tiles from existing tileset
  - Banana Bot NPC sprite sheet
  - Farm building decorative sprites
  - Banana tree sprites with growth stages

### 3.3 Idle Generation Logic

#### 3.3.1 Calculation System
```typescript
class IdleGenerator {
  calculateOfflineEarnings(farmState: FarmState): number {
    const now = Date.now();
    const timeDiff = now - farmState.lastUpdateTime;
    const minutesOffline = timeDiff / (1000 * 60);
    return Math.floor(minutesOffline * farmState.generationRate);
  }
  
  updateGenerationRate(botLevel: number, plantationLevel: number): number {
    const baseRate = this.getBotRate(botLevel);
    const multiplier = this.getPlantationMultiplier(plantationLevel);
    return baseRate * multiplier;
  }
}
```

---

## 4. User Experience Flow

### 4.1 Discovery Flow
1. **Exploration Trigger:** Player moves monkey to right edge of jungle scene
2. **Visual Cue:** Subtle arrow or path indicator suggesting continuation
3. **Scene Transition:** Smooth fade to farm scene with monkey entering from left
4. **First Visit:** Tutorial tooltip introducing the Banana Bot NPC

### 4.2 Interaction Flow
1. **NPC Approach:** Player moves monkey near Banana Bot
2. **Interaction Prompt:** "Press SPACE to talk" indicator appears
3. **Dialog Opening:** Shop interface slides up from bottom
4. **Purchase Flow:** Select upgrade → Confirm cost → Visual feedback
5. **Immediate Effect:** Generation rate updates, visual bots appear

### 4.3 Offline Return Flow
1. **App Launch:** Calculate offline earnings automatically
2. **Welcome Back Modal:** "You earned X bananas while away!"
3. **Collection Animation:** Bananas fly into counter with satisfying effects
4. **Progress Notification:** Highlight any newly affordable upgrades

---

## 5. Success Metrics

### 5.1 Engagement Metrics
- **Scene Discovery Rate:** % of players who find the farm scene
- **NPC Interaction Rate:** % of farm visitors who interact with Banana Bot
- **Upgrade Purchase Rate:** % of players who buy at least one farm upgrade
- **Return Frequency:** Average sessions per day after farm unlock

### 5.2 Retention Metrics
- **Offline Engagement:** % of players who return within 24 hours of farm unlock
- **Progression Rate:** Average time to reach each upgrade tier
- **Session Length:** Increase in average session duration post-farm

---

## 6. Implementation Timeline

### 6.1 Phase 1: Core Infrastructure (Week 1)
- [ ] Update type definitions for new scene and farm state
- [ ] Implement scene transition system
- [ ] Create basic farm scene with tileset integration
- [ ] Add Banana Bot NPC sprite and positioning

### 6.2 Phase 2: Idle System (Week 2)
- [ ] Implement offline calculation logic
- [ ] Create upgrade system integration
- [ ] Build NPC interaction dialog system
- [ ] Add visual feedback for generation

### 6.3 Phase 3: Polish & Testing (Week 3)
- [ ] Implement smooth scene transitions
- [ ] Add tutorial and onboarding flow
- [ ] Create offline earnings modal
- [ ] Comprehensive testing and bug fixes

---

## 7. Risk Assessment

### 7.1 Technical Risks
- **Performance Impact:** Additional scene may affect mobile performance
  - *Mitigation:* Lazy loading and asset optimization
- **Save State Complexity:** Offline calculations need reliable timestamp storage
  - *Mitigation:* Robust error handling and fallback mechanisms

### 7.2 Design Risks
- **Feature Discoverability:** Players might miss the farm scene
  - *Mitigation:* Clear visual cues and optional tutorial
- **Balance Issues:** Idle generation might make active play less rewarding
  - *Mitigation:* Careful tuning and player feedback integration

---

## 8. Future Enhancements

### 8.1 Expansion Opportunities
- **Additional NPCs:** Farm worker characters with specialized functions
- **Seasonal Events:** Limited-time farm decorations and bonuses
- **Social Features:** Visit friends' farms and trade resources
- **Mini-Games:** Harvest timing games for bonus rewards

### 8.2 Integration Points
- **Quest System:** Farm-specific daily/weekly quests
- **Achievement System:** Farm milestone achievements
- **Cosmetic Rewards:** Farm-themed monkey outfits and decorations
