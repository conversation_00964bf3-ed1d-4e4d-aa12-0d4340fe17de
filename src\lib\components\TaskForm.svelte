<script lang="ts">
  import { taskStore } from '$lib/stores';
  import { BananaCalculator } from '$lib/utils/bananaCalculator';
  import type { Task } from '$lib/types';

  // Form state
  let title = '';
  let description = '';
  let priority: 'low' | 'medium' | 'high' = 'medium';
  let category = '';
  let dueDate = '';
  
  // Calculated banana reward
  $: bananaReward = BananaCalculator.getInstance().calculateTaskReward({
    title,
    description,
    priority,
    category,
    dueDate: dueDate ? new Date(dueDate) : undefined,
    completed: false,
    bananaReward: 0 // This will be overwritten by the calculation
  });

  function handleSubmit() {
    if (!title.trim()) return;
    
    const newTask: Omit<Task, 'id' | 'createdAt'> = {
      title: title.trim(),
      description: description.trim(),
      priority,
      category: category.trim(),
      dueDate: dueDate ? new Date(dueDate) : undefined,
      completed: false,
      bananaReward
    };

    taskStore.add(newTask);
    
    // Reset form
    title = '';
    description = '';
    priority = 'medium';
    category = '';
    dueDate = '';
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleSubmit();
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="task-form">
  <h3>Add New Task</h3>
  
  <div class="form-group">
    <label for="title">Task Title *</label>
    <input
      id="title"
      type="text"
      bind:value={title}
      on:keydown={handleKeydown}
      placeholder="What needs to be done?"
      required
    />
  </div>
  
  <div class="form-group">
    <label for="description">Description</label>
    <textarea
      id="description"
      bind:value={description}
      on:keydown={handleKeydown}
      placeholder="Add more details (optional)"
      rows="3"
    ></textarea>
  </div>
  
  <div class="form-row">
    <div class="form-group">
      <label for="priority">Priority</label>
      <select id="priority" bind:value={priority}>
        <option value="low">Low</option>
        <option value="medium">Medium</option>
        <option value="high">High</option>
      </select>
    </div>
    
    <div class="form-group">
      <label for="category">Category</label>
      <input
        id="category"
        type="text"
        bind:value={category}
        placeholder="e.g., Work, Personal"
      />
    </div>
    
    <div class="form-group">
      <label for="dueDate">Due Date</label>
      <input
        id="dueDate"
        type="date"
        bind:value={dueDate}
      />
    </div>
  </div>
  
  <div class="reward-preview">
    <span class="banana-icon">🍌</span>
    <span class="reward-text">Reward: {bananaReward} bananas</span>
  </div>
  
  <button type="submit" disabled={!title.trim()} class="submit-btn">
    Add Task
  </button>
  
  <p class="form-hint">
    💡 Tip: Press Ctrl+Enter to quickly add the task
  </p>
</form>

<style>
  .task-form {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid #E5E7EB;
    margin-bottom: 1.5rem;
  }
  
  .task-form h3 {
    margin: 0 0 1rem 0;
    color: #374151;
    font-size: 1.25rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
    font-size: 0.9rem;
  }
  
  input, textarea, select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
    box-sizing: border-box;
  }
  
  input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #10B981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }
  
  textarea {
    resize: vertical;
    min-height: 80px;
  }
  
  .reward-preview {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #F0FDF4;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #BBF7D0;
  }
  
  .banana-icon {
    font-size: 1.2rem;
  }
  
  .reward-text {
    font-weight: 500;
    color: #059669;
  }
  
  .submit-btn {
    width: 100%;
    background: #10B981;
    color: white;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .submit-btn:hover:not(:disabled) {
    background: #059669;
  }
  
  .submit-btn:disabled {
    background: #9CA3AF;
    cursor: not-allowed;
  }
  
  .form-hint {
    margin: 0.5rem 0 0 0;
    font-size: 0.8rem;
    color: #6B7280;
    text-align: center;
    font-style: italic;
  }
  
  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }
    
    .task-form {
      padding: 1rem;
    }
  }
</style>
