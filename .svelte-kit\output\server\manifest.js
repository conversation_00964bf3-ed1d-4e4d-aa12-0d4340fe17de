export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["assets/banana/Banana.png","assets/monkey/Dead.png","assets/monkey/Hit.png","assets/monkey/Idle.png","assets/monkey/Jump.png","assets/monkey/Run.png","assets/Platforms.png","assets/README.md","assets/Tileset-Spritesheet.png","favicon.svg"]),
	mimeTypes: {".png":"image/png",".md":"text/markdown",".svg":"image/svg+xml"},
	_: {
		client: {start:"_app/immutable/entry/start.CM8QSaDT.js",app:"_app/immutable/entry/app.HZc8k4-V.js",imports:["_app/immutable/entry/start.CM8QSaDT.js","_app/immutable/chunks/C2NQNm1d.js","_app/immutable/chunks/C_1qu2iM.js","_app/immutable/chunks/QsgQHiSo.js","_app/immutable/entry/app.HZc8k4-V.js","_app/immutable/chunks/yuiQqa5R.js","_app/immutable/chunks/QsgQHiSo.js","_app/immutable/chunks/C_1qu2iM.js","_app/immutable/chunks/CWj6FrbW.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
