// Banana Checklist - Type definitions

export interface Task {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  category?: string;
  dueDate?: Date;
  createdAt: Date;
  completedAt?: Date;
  bananaReward: number;
}

export interface Goal {
  id: string;
  title: string;
  description?: string;
  milestones: Milestone[];
  category?: string;
  targetDate?: Date;
  createdAt: Date;
  completedAt?: Date;
  totalBananaReward: number;
}

export interface Milestone {
  id: string;
  title: string;
  completed: boolean;
  bananaReward: number;
  completedAt?: Date;
}

export interface User {
  id: string;
  bananaCount: number;
  totalTasksCompleted: number;
  totalGoalsCompleted: number;
  unlockedFeatures: string[];
  isPremium: boolean;
  createdAt: Date;
  lastActiveAt: Date;
}

export interface GameState {
  monkeyPosition: { x: number; y: number };
  currentScene: 'jungle' | 'grove' | 'minigame';
  unlockedAreas: string[];
  cosmetics: {
    monkeySkin: string;
    hat?: string;
    theme: string;
  };
  upgrades: GameUpgrade[];
}

export interface GameUpgrade {
  id: string;
  name: string;
  description: string;
  cost: number;
  purchased: boolean;
  effect: string;
}

export interface Quest {
  id: string;
  title: string;
  description: string;
  type: 'daily' | 'weekly' | 'achievement';
  requirements: QuestRequirement[];
  bananaReward: number;
  completed: boolean;
  progress: number;
  maxProgress: number;
}

export interface QuestRequirement {
  type: 'complete_tasks' | 'maintain_streak' | 'collect_bananas';
  target: number;
  current: number;
}

export interface AppSettings {
  soundEnabled: boolean;
  musicEnabled: boolean;
  retroFilter: boolean;
  highContrast: boolean;
  animationsEnabled: boolean;
}

// Game-specific types
export interface Sprite {
  x: number;
  y: number;
  width: number;
  height: number;
  texture: string;
  visible: boolean;
}

export interface GameInput {
  left: boolean;
  right: boolean;
  up: boolean;
  down: boolean;
  jump: boolean;
  interact: boolean;
}

// API types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface TaskCreateRequest {
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high';
  category?: string;
  dueDate?: string;
}

export interface TaskUpdateRequest {
  title?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  category?: string;
  dueDate?: string;
  completed?: boolean;
}
