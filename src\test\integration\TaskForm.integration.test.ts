import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import userEvent from '@testing-library/user-event';
import { taskStore, userStore } from '$lib/stores';
import TaskForm from '$lib/components/TaskForm.svelte';
import { get } from 'svelte/store';

describe('TaskForm Integration Tests', () => {
    let user: ReturnType<typeof userEvent.setup>;

    beforeEach(() => {
        user = userEvent.setup();

        // Reset stores before each test
        taskStore.clear();
        userStore.set({
            id: 'test-user',
            bananaCount: 100,
            unlockedFeatures: [],
            isPremium: false,
            createdAt: new Date(),
            lastActiveAt: new Date()
        });
    });

    describe('Form Rendering', () => {
        it('should render task form with all required fields', () => {
            render(TaskForm);

            expect(screen.getByLabelText(/task title/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/priority/i)).toBeInTheDocument();
            expect(screen.getByRole('button', { name: /add task/i })).toBeInTheDocument();
        });

        it('should have proper form validation attributes', () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            expect(titleInput).toHaveAttribute('required');
            expect(titleInput).toHaveAttribute('maxlength', '100');
        });
    });

    describe('Form Interaction', () => {
        it('should allow typing in all form fields', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const descriptionInput = screen.getByLabelText(/description/i);
            const categorySelect = screen.getByLabelText(/category/i);
            const prioritySelect = screen.getByLabelText(/priority/i);

            await user.type(titleInput, 'Test Task');
            await user.type(descriptionInput, 'Test Description');
            await user.selectOptions(categorySelect, 'work');
            await user.selectOptions(prioritySelect, 'high');

            expect(titleInput).toHaveValue('Test Task');
            expect(descriptionInput).toHaveValue('Test Description');
            expect(categorySelect).toHaveValue('work');
            expect(prioritySelect).toHaveValue('high');
        });

        it('should clear form after successful submission', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const descriptionInput = screen.getByLabelText(/description/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            await user.type(titleInput, 'Test Task');
            await user.type(descriptionInput, 'Test Description');
            await user.click(submitButton);

            expect(titleInput).toHaveValue('');
            expect(descriptionInput).toHaveValue('');
        });
    });

    describe('Task Creation', () => {
        it('should create a new task when form is submitted', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const descriptionInput = screen.getByLabelText(/description/i);
            const categorySelect = screen.getByLabelText(/category/i);
            const prioritySelect = screen.getByLabelText(/priority/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            await user.type(titleInput, 'Integration Test Task');
            await user.type(descriptionInput, 'This is a test task for integration testing');
            await user.selectOptions(categorySelect, 'personal');
            await user.selectOptions(prioritySelect, 'medium');
            await user.click(submitButton);

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(1);
            expect(tasks[0].title).toBe('Integration Test Task');
            expect(tasks[0].description).toBe('This is a test task for integration testing');
            expect(tasks[0].category).toBe('personal');
            expect(tasks[0].priority).toBe('medium');
            expect(tasks[0].completed).toBe(false);
            expect(tasks[0].id).toBeDefined();
            expect(tasks[0].createdAt).toBeInstanceOf(Date);
        });

        it('should create task with default values when optional fields are empty', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            await user.type(titleInput, 'Minimal Task');
            await user.click(submitButton);

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(1);
            expect(tasks[0].title).toBe('Minimal Task');
            expect(tasks[0].description).toBe('');
            expect(tasks[0].category).toBe('personal'); // default category
            expect(tasks[0].priority).toBe('medium'); // default priority
        });

        it('should not submit form when title is empty', async () => {
            render(TaskForm);

            const submitButton = screen.getByRole('button', { name: /add task/i });

            await user.click(submitButton);

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(0);
        });

        it('should handle form submission with Enter key', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);

            await user.type(titleInput, 'Enter Key Task');
            await user.keyboard('{Enter}');

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(1);
            expect(tasks[0].title).toBe('Enter Key Task');
        });
    });

    describe('Form Validation', () => {
        it('should show validation error for empty title', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            // Try to submit empty form
            await user.click(submitButton);

            // HTML5 validation should prevent submission
            expect(titleInput).toBeInvalid();
        });

        it('should enforce maximum length on title field', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const longTitle = 'a'.repeat(150); // Longer than maxlength

            await user.type(titleInput, longTitle);

            // Should be truncated to maxlength
            expect(titleInput.value.length).toBeLessThanOrEqual(100);
        });
    });

    describe('Category and Priority Options', () => {
        it('should have all expected category options', () => {
            render(TaskForm);

            const categorySelect = screen.getByLabelText(/category/i);
            const options = Array.from(categorySelect.querySelectorAll('option')).map(opt => opt.value);

            expect(options).toContain('personal');
            expect(options).toContain('work');
            expect(options).toContain('health');
            expect(options).toContain('learning');
            expect(options).toContain('other');
        });

        it('should have all expected priority options', () => {
            render(TaskForm);

            const prioritySelect = screen.getByLabelText(/priority/i);
            const options = Array.from(prioritySelect.querySelectorAll('option')).map(opt => opt.value);

            expect(options).toContain('low');
            expect(options).toContain('medium');
            expect(options).toContain('high');
            expect(options).toContain('urgent');
        });
    });

    describe('Store Integration', () => {
        it('should update task store when multiple tasks are added', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            // Add first task
            await user.type(titleInput, 'First Task');
            await user.click(submitButton);

            // Add second task
            await user.type(titleInput, 'Second Task');
            await user.click(submitButton);

            const tasks = get(taskStore);
            expect(tasks).toHaveLength(2);
            expect(tasks[0].title).toBe('First Task');
            expect(tasks[1].title).toBe('Second Task');
        });

        it('should maintain task order in store', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            const taskTitles = ['Alpha', 'Beta', 'Gamma'];

            for (const title of taskTitles) {
                await user.type(titleInput, title);
                await user.click(submitButton);
            }

            const tasks = get(taskStore);
            expect(tasks.map(t => t.title)).toEqual(taskTitles);
        });
    });

    describe('Accessibility', () => {
        it('should have proper ARIA labels and form structure', () => {
            render(TaskForm);

            expect(screen.getByRole('form')).toBeInTheDocument();
            expect(screen.getByLabelText(/task title/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
            expect(screen.getByLabelText(/priority/i)).toBeInTheDocument();
        });

        it('should support keyboard navigation', async () => {
            render(TaskForm);

            const titleInput = screen.getByLabelText(/task title/i);
            const descriptionInput = screen.getByLabelText(/description/i);
            const categorySelect = screen.getByLabelText(/category/i);
            const prioritySelect = screen.getByLabelText(/priority/i);
            const submitButton = screen.getByRole('button', { name: /add task/i });

            // Tab through form elements
            titleInput.focus();
            await user.keyboard('{Tab}');
            expect(descriptionInput).toHaveFocus();

            await user.keyboard('{Tab}');
            expect(categorySelect).toHaveFocus();

            await user.keyboard('{Tab}');
            expect(prioritySelect).toHaveFocus();

            await user.keyboard('{Tab}');
            expect(submitButton).toHaveFocus();
        });
    });
});
