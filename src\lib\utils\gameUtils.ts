import type { GameInput, Sprite } from '../types';

/**
 * Game utility functions for PixiJS integration
 */

/**
 * Check collision between two rectangular sprites
 */
export function checkCollision(sprite1: Sprite, sprite2: Sprite): boolean {
  return (
    sprite1.x < sprite2.x + sprite2.width &&
    sprite1.x + sprite1.width > sprite2.x &&
    sprite1.y < sprite2.y + sprite2.height &&
    sprite1.y + sprite1.height > sprite2.y
  );
}

/**
 * Calculate distance between two points
 */
export function calculateDistance(x1: number, y1: number, x2: number, y2: number): number {
  const dx = x2 - x1;
  const dy = y2 - y1;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Clamp a value between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Linear interpolation between two values
 */
export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * factor;
}

/**
 * Convert degrees to radians
 */
export function degreesToRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Convert radians to degrees
 */
export function radiansToDegrees(radians: number): number {
  return radians * (180 / Math.PI);
}

/**
 * Generate a random number between min and max
 */
export function randomBetween(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

/**
 * Generate a random integer between min and max (inclusive)
 */
export function randomIntBetween(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Check if a point is within a rectangular area
 */
export function pointInRect(
  pointX: number, 
  pointY: number, 
  rectX: number, 
  rectY: number, 
  rectWidth: number, 
  rectHeight: number
): boolean {
  return (
    pointX >= rectX &&
    pointX <= rectX + rectWidth &&
    pointY >= rectY &&
    pointY <= rectY + rectHeight
  );
}

/**
 * Normalize a vector
 */
export function normalizeVector(x: number, y: number): { x: number; y: number } {
  const length = Math.sqrt(x * x + y * y);
  if (length === 0) return { x: 0, y: 0 };
  return { x: x / length, y: y / length };
}

/**
 * Apply physics movement with gravity
 */
export function applyPhysics(
  position: { x: number; y: number },
  velocity: { x: number; y: number },
  gravity: number = 0.5,
  friction: number = 0.8,
  deltaTime: number = 1
): { position: { x: number; y: number }; velocity: { x: number; y: number } } {
  // Apply gravity to vertical velocity
  velocity.y += gravity * deltaTime;
  
  // Apply friction to horizontal velocity
  velocity.x *= friction;
  
  // Update position
  position.x += velocity.x * deltaTime;
  position.y += velocity.y * deltaTime;
  
  return { position, velocity };
}

/**
 * Handle keyboard input for game controls
 */
export function updateInputFromKeyboard(
  input: GameInput,
  keysPressed: Set<string>
): GameInput {
  return {
    left: keysPressed.has('ArrowLeft') || keysPressed.has('KeyA'),
    right: keysPressed.has('ArrowRight') || keysPressed.has('KeyD'),
    up: keysPressed.has('ArrowUp') || keysPressed.has('KeyW'),
    down: keysPressed.has('ArrowDown') || keysPressed.has('KeyS'),
    jump: keysPressed.has('Space'),
    interact: keysPressed.has('KeyE') || keysPressed.has('Enter')
  };
}

/**
 * Create a simple easing function for animations
 */
export function easeInOutQuad(t: number): number {
  return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
}

/**
 * Create a bounce easing function
 */
export function easeOutBounce(t: number): number {
  if (t < 1 / 2.75) {
    return 7.5625 * t * t;
  } else if (t < 2 / 2.75) {
    return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
  } else if (t < 2.5 / 2.75) {
    return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
  } else {
    return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
  }
}

/**
 * Screen shake effect parameters
 */
export function calculateScreenShake(
  intensity: number,
  duration: number,
  currentTime: number
): { x: number; y: number } {
  if (currentTime > duration) return { x: 0, y: 0 };
  
  const progress = currentTime / duration;
  const currentIntensity = intensity * (1 - progress);
  
  return {
    x: (Math.random() - 0.5) * currentIntensity,
    y: (Math.random() - 0.5) * currentIntensity
  };
}

/**
 * Particle system helper for banana collection effects
 */
export interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: number;
  scale: number;
}

export function createBananaParticles(x: number, y: number, count: number = 10): Particle[] {
  const particles: Particle[] = [];
  
  for (let i = 0; i < count; i++) {
    particles.push({
      x,
      y,
      vx: randomBetween(-3, 3),
      vy: randomBetween(-5, -1),
      life: 60, // 60 frames at 60fps = 1 second
      maxLife: 60,
      color: 0xFFD700, // Gold color for banana particles
      scale: randomBetween(0.5, 1.5)
    });
  }
  
  return particles;
}

export function updateParticles(particles: Particle[]): Particle[] {
  return particles
    .map(particle => ({
      ...particle,
      x: particle.x + particle.vx,
      y: particle.y + particle.vy,
      vy: particle.vy + 0.1, // Gravity
      life: particle.life - 1,
      scale: particle.scale * 0.98 // Shrink over time
    }))
    .filter(particle => particle.life > 0);
}
