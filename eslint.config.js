import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';
import path from 'path';
import { fileURLToPath } from 'url';
import tseslint from 'typescript-eslint';
import sveltePlugin from 'eslint-plugin-svelte';
import svelteParser from 'svelte-eslint-parser';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
	baseDirectory: __dirname,
	recommendedConfig: js.configs.recommended,
});

export default tseslint.config(
	js.configs.recommended,
	...tseslint.configs.recommended,
	...sveltePlugin.configs['flat/recommended'],
	{
		languageOptions: {
			globals: {
				console: 'readonly',
				process: 'readonly',
				window: 'readonly',
				document: 'readonly',
				confirm: 'readonly',
				alert: 'readonly',
				localStorage: 'readonly',
				sessionStorage: 'readonly',
			},
			parserOptions: {
				project: './tsconfig.json',
				extraFileExtensions: ['.svelte'],
			},
		},
	},
	{
		files: ['**/*.svelte'],
		languageOptions: {
			parser: svelteParser,
			parserOptions: {
				parser: tseslint.parser,
			},
		},
	},
	{
		files: ['**/*.{js,ts,svelte}'],
		rules: {
			// TypeScript specific rules
			'@typescript-eslint/no-unused-vars': [
				'error',
				{ argsIgnorePattern: '^_' },
			],
			'@typescript-eslint/no-explicit-any': 'warn',

			// General code quality rules
			'no-console': 'warn',
			'no-debugger': 'error',
			'no-duplicate-imports': 'error',
			'no-unused-vars': 'off', // Use TypeScript version instead

			// Svelte specific rules
			'svelte/no-at-debug-tags': 'error',
			'svelte/no-target-blank': 'error',
			'svelte/no-at-html-tags': 'warn',

			// Code style rules
			'prefer-const': 'error',
			'no-var': 'error',
			'object-shorthand': 'error',
			'prefer-template': 'error',
		},
	},
	{
		files: ['**/*.test.{js,ts}', '**/*.spec.{js,ts}', 'src/test/**/*'],
		languageOptions: {
			globals: {
				vi: 'readonly',
				describe: 'readonly',
				it: 'readonly',
				expect: 'readonly',
				beforeEach: 'readonly',
				afterEach: 'readonly',
				beforeAll: 'readonly',
				afterAll: 'readonly',
			},
		},
		rules: {
			// Relax rules for test files
			'@typescript-eslint/no-explicit-any': 'off',
			'no-console': 'off',
		},
	},
	{
		ignores: [
			'build/',
			'.svelte-kit/',
			'dist/',
			'node_modules/',
			'*.config.js',
			'*.config.ts',
			'coverage/',
		],
	}
);
